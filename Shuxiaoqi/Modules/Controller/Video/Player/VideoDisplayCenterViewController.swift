//
//  VideoDisplayCenterViewController.swift
//  Shu<PERSON><PERSON>qi
//
//  Created by yong<PERSON>ng ye on 2025/3/26.
//

//  视频展示页

import UIKit
import Foundation
import SnapKit
import TXLiteAVSDK_UGC
import CoreImage

/**
 `VideoDisplayCenterViewController`
 
 播放器模块的核心容器 VC，负责纵向翻页播放短视频，功能涵盖：
 1. **数据源管理**：通过 `APIManager` 分页拉取视频流，支持外部指定 `videoItem` 作为起始视频。
 2. **页面复用/缓存**：结合 `UIPageViewController` 实现的页面滑动，维护 `videoPages` 数组；通过 `cleanupPages()` 保持前后各 2 页内存占用。
 3. **播放器生命周期**：在页面切换、VC 出现/消失、deinit 等时机统一暂停/释放 `TXVodPlayer`，避免资源泄漏。
 4. **交互入口**：自定义导航栏（返回 / 搜索 / 更多）与 `VideoPageDelegate` 分享回调。
 5. **嵌入灵活**：通过外部参数 `hideNavBackButton`、`showCustomNavBar`、`needsTabBarOffset` 适配不同父级场景（如 Tab 页、嵌套列表等）。
 
 资源管理策略：
 - **短视频常见滑动释放策略**：当滑动到第 N 页时，立即释放 N-2 之前的页面播放器，保持最多 5 个页面驻留。
 - **初始化保护**：`initializePlayerIfNeeded()` 防止重复创建播放器，保证每个页面仅 1 个 `TXVodPlayer` 实例。
 - **出错降级**：播放参数缺失时回退到错误占位视图，页面仍可正常滑动。
 
 日志打印已加入关键信息，可通过 Xcode Console 快速排查页面重复创建或资源未释放问题。
 */
class VideoDisplayCenterViewController: UIViewController {

    // MARK: - Types
    enum VideoListLoadType {
        case recommend  // 推荐流
        case follow     // 关注流
        case friend     // 朋友流
        case city       // 同城流
    }

    // MARK: - Properties
    private var currentIndex = 0
    private let pageSize = 3 // 预加载数量
    private var videoPages: [VideoPage] = []
    private var videoList: [VideoItem] = [] // 存储视频流列表
    // 外部可直接注入的完整视频列表（用于我的作品、喜欢、收藏等固定池）
    private var providedVideoList: [VideoItem]? = nil
    // 外部指定的初始索引，默认0
    private var initialIndex: Int = 0
    private var isLoadingMore = false // 是否正在加载更多
    private var currentPage = 0 // 当前页码
    private var hasMoreData = true // 是否还有更多数据
    private let loadSize = 10 // 每次加载的数量
    private var hasInitiallyLoaded = false // 是否已经完成了首次加载

    // 视频列表类型，用于根据不同入口请求不同的列表数据
    // -1: 单视频模式（不加载列表，用于个人作品、收藏、点赞等）
    // -2: 草稿预览模式（不加载列表，禁用交互，用于草稿预览）
    // -3: 内容管理预览模式（不加载列表，禁用交互，用于内容管理中非已发布作品预览）
    // 0: 推荐流, 1: 关注流, 3: 朋友流, 4: 同城流
    public var videoListType: Int = 0

    // 同城相关属性
    public var areaCode: String?
    
    // 是否隐藏返回按钮，外部可配置
    public var hideNavBackButton: Bool = false
    // 是否需要为自定义 TabBar 预留底部间距 (58)
    public var needsTabBarOffset: Bool = true
    // 是否展示自定义导航栏，外部可配置 (默认为显示)
    public var showCustomNavBar: Bool = true
    
    // UI组件
    private let ui = VideoDisplayCenterUIComponents()

    // 侧滑返回手势相关
    private var panGestureRecognizer: UIPanGestureRecognizer?
    private var interactiveTransition: UIPercentDrivenInteractiveTransition?

    // 朋友页面占位视图
    private var friendEmptyPlaceholderView: FriendEmptyPlaceholderView?

    // MARK: - 数据模型
    /// 若由首页推荐或搜索点击单个视频进入，则可传入完整的视频数据
    public var videoItem: VideoItem?
    
    /// 便利初始化：直接传入 VideoItem（推荐流模式，会拼接推荐视频）
    convenience init(videoItem: VideoItem,
                     hideNavBackButton: Bool = false,
                     showCustomNavBar: Bool = true,
                     needsTabBarOffset: Bool = true) {
        self.init()
        self.videoItem = videoItem
        self.videoListType = 0 // 明确设置为推荐流
        self.hideNavBackButton = hideNavBackButton
        self.showCustomNavBar = showCustomNavBar
        self.needsTabBarOffset = needsTabBarOffset
    }

    /// 便利初始化：关注流模式（会拼接关注视频）
    convenience init(followVideoItem: VideoItem,
                     hideNavBackButton: Bool = false,
                     showCustomNavBar: Bool = true,
                     needsTabBarOffset: Bool = true) {
        self.init()
        self.videoItem = followVideoItem
        self.videoListType = 1 // 明确设置为关注流
        self.hideNavBackButton = hideNavBackButton
        self.showCustomNavBar = showCustomNavBar
        self.needsTabBarOffset = needsTabBarOffset
    }

    /// 便利初始化：朋友流模式（会拼接朋友视频）
    convenience init(friendVideoItem: VideoItem,
                     hideNavBackButton: Bool = false,
                     showCustomNavBar: Bool = true,
                     needsTabBarOffset: Bool = true) {
        self.init()
        self.videoItem = friendVideoItem
        self.videoListType = 3 // 明确设置为朋友流
        self.hideNavBackButton = hideNavBackButton
        self.showCustomNavBar = showCustomNavBar
        self.needsTabBarOffset = needsTabBarOffset
    }

    /// 便利初始化：单个视频播放（不加载列表，主要用于个人作品、收藏、点赞等）
    convenience init(singleVideoItem: VideoItem,
                     hideNavBackButton: Bool = false,
                     showCustomNavBar: Bool = true,
                     needsTabBarOffset: Bool = true) {
        self.init()
        self.videoItem = singleVideoItem
        self.videoListType = -1 // 使用-1表示单视频模式，不加载列表
        self.hideNavBackButton = hideNavBackButton
        self.showCustomNavBar = showCustomNavBar
        self.needsTabBarOffset = needsTabBarOffset
    }

    /// 便利初始化：草稿预览模式（不加载列表，禁用交互，用于草稿预览）
    convenience init(draftPreviewItem: VideoItem,
                     hideNavBackButton: Bool = false,
                     showCustomNavBar: Bool = true,
                     needsTabBarOffset: Bool = true) {
        self.init()
        self.videoItem = draftPreviewItem
        self.videoListType = -2 // 使用-2表示草稿预览模式，不加载列表且禁用交互
        self.hideNavBackButton = hideNavBackButton
        self.showCustomNavBar = showCustomNavBar
        self.needsTabBarOffset = needsTabBarOffset
    }

    /// 便利初始化：内容管理预览模式（不加载列表，禁用交互，用于内容管理中非已发布作品预览）
    convenience init(contentManagementPreviewItem: VideoItem,
                     hideNavBackButton: Bool = false,
                     showCustomNavBar: Bool = true,
                     needsTabBarOffset: Bool = true) {
        self.init()
        self.videoItem = contentManagementPreviewItem
        self.videoListType = -3 // 使用-3表示内容管理预览模式，不加载列表且禁用交互
        self.hideNavBackButton = hideNavBackButton
        self.showCustomNavBar = showCustomNavBar
        self.needsTabBarOffset = needsTabBarOffset
    }

    /// 便利初始化：直接传入完整视频集合 + 初始索引，内部不再拉流
    convenience init(videoList: [VideoItem],
                     startIndex: Int = 0,
                     hideNavBackButton: Bool = false,
                     showCustomNavBar: Bool = true,
                     needsTabBarOffset: Bool = true) {
        self.init()

        print("=== VideoDisplayCenterViewController 初始化 ===")
        print("接收到的视频列表数量: \(videoList.count)")
        print("接收到的起始索引: \(startIndex)")

        // 打印前几个视频信息
        for (index, item) in videoList.prefix(min(5, videoList.count)).enumerated() {
            let marker = index == startIndex ? "👉" : "  "
            print("\(marker) 视频[\(index)]: ID=\(item.id ?? -1), 标题=\(item.worksTitle ?? "无标题")")
        }
        print("==========================================")

        self.providedVideoList = videoList
        self.initialIndex = startIndex
        self.hideNavBackButton = hideNavBackButton
        self.showCustomNavBar = showCustomNavBar
        self.needsTabBarOffset = needsTabBarOffset
    }
    
    /// 便利初始化方法，允许在创建时直接传入类型及导航栏配置
    convenience init(
        videoListType: Int,
        hideNavBackButton: Bool = false,
        showCustomNavBar: Bool = true,
        needsTabBarOffset: Bool = true
    ) {
        self.init()
        self.videoListType = videoListType
        self.hideNavBackButton = hideNavBackButton
        self.showCustomNavBar = showCustomNavBar
        self.needsTabBarOffset = needsTabBarOffset
    }

    /// 便利初始化：同城视频播放，传入当前视频item和同城标识
    convenience init(
        videoItem: VideoItem,
        areaCode: String,
        hideNavBackButton: Bool = false,
        showCustomNavBar: Bool = true,
        needsTabBarOffset: Bool = true
    ) {
        self.init()
        self.videoItem = videoItem
        self.videoListType = 4 // 同城流
        self.areaCode = areaCode
        self.hideNavBackButton = hideNavBackButton
        self.showCustomNavBar = showCustomNavBar
        self.needsTabBarOffset = needsTabBarOffset

        print("[VideoDisplay] 同城初始化：videoItem=\(videoItem.worksTitle ?? "未知"), areaCode=\(areaCode)")
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        
        // Debug: 打印传入的视频数据，确保值正确
        if let item = videoItem {
            let vid = item.id != nil ? String(item.id!) : "nil"
            print("[VideoDisplay] Received VideoItem -> id: \(vid), title: \(item.worksTitle ?? "nil"), sign: \(item.playSign ?? "nil"), videoId: \(item.videoId ?? "nil")")
        }
        
        // 若外部已提供列表，则直接使用，不再请求推荐流
        if let list = providedVideoList, !list.isEmpty {
            print("=== VideoDisplayCenterViewController 设置视频列表 ===")
            print("设置前 videoList 数量: \(self.videoList.count)")
            print("providedVideoList 数量: \(list.count)")

            self.videoList = list

            print("设置后 videoList 数量: \(self.videoList.count)")
            // 打印前几个视频信息确认数据正确
            for (index, item) in self.videoList.prefix(min(5, self.videoList.count)).enumerated() {
                print("videoList[\(index)]: ID=\(item.id ?? -1), 标题=\(item.worksTitle ?? "无标题")")
            }
            print("============================================")

            self.hasMoreData = false // 关闭分页加载
            self.setupInitialPages()
        } else if videoListType == -1 {
            // 单视频模式：只播放传入的视频，不加载列表
            if let item = videoItem {
                self.videoList = [item]
                self.hasMoreData = false // 关闭分页加载
                self.setupInitialPages()
                print("[VideoDisplay] 单视频模式：只播放传入的视频，不加载更多")
            } else {
                print("[VideoDisplay] 单视频模式：没有传入的videoItem，创建空页面")
                self.setupInitialPages()
            }
        } else if videoListType == -2 {
            // 草稿预览模式：只播放传入的视频，不加载列表，禁用交互
            if let item = videoItem {
                self.videoList = [item]
                self.hasMoreData = false // 关闭分页加载
                self.setupInitialPages()
                print("[VideoDisplay] 草稿预览模式：只播放传入的视频，禁用交互")
            } else {
                print("[VideoDisplay] 草稿预览模式：没有传入的videoItem，创建空页面")
                self.setupInitialPages()
            }
        } else if videoListType == -3 {
            // 内容管理预览模式：只播放传入的视频，不加载列表，禁用交互
            if let item = videoItem {
                self.videoList = [item]
                self.hasMoreData = false // 关闭分页加载
                self.setupInitialPages()
                print("[VideoDisplay] 内容管理预览模式：只播放传入的视频，禁用交互")
            } else {
                print("[VideoDisplay] 内容管理预览模式：没有传入的videoItem，创建空页面")
                self.setupInitialPages()
            }
        } else if videoListType == 0 && videoItem != nil {
            // 推荐流且有传入的videoItem：先显示传入的视频，然后加载推荐流拼接
            self.setupInitialVideoItemAndLoadMore(listType: .recommend)
        } else if videoListType == 1 && videoItem != nil {
            // 关注流且有传入的videoItem：先显示传入的视频，然后加载关注流拼接
            self.setupInitialVideoItemAndLoadMore(listType: .follow)
        } else if videoListType == 3 && videoItem != nil {
            // 朋友流且有传入的videoItem：先显示传入的视频，然后加载朋友流拼接
            self.setupInitialVideoItemAndLoadMore(listType: .friend)
        } else if videoListType == 4 && videoItem != nil {
            // 同城视频播放：先将传入的视频添加到列表，然后加载更多同城视频
            self.setupInitialVideoItemAndLoadMore(listType: .city)
        } else {
            // 其他情况：直接初始化视频流（关注流、朋友流等）
            fetchVideoStream()
        }
        
        // 隐藏系统导航栏
        navigationController?.setNavigationBarHidden(true, animated: false)

        // 设置侧滑返回手势
        setupSwipeBackGesture()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationController?.setNavigationBarHidden(true, animated: false)
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)

        // 页面消失时停止所有播放器
        for page in videoPages {
            if let videoPage = page as? VideoPage {
                videoPage.player?.pause()
                videoPage.isPlaying = false
            }
        }

        // 当从视频页面返回时，确保目标页面的 TabBar 状态正确
        if isMovingFromParent || isBeingDismissed {
            // 检查返回的目标页面是否是二级页面（非 TabBar 根视图控制器）
            if let navController = navigationController,
               navController.viewControllers.count > 1,
               let targetVC = navController.viewControllers[navController.viewControllers.count - 2] as? BaseViewController,
               !targetVC.isTabBarRootViewController {

                // 如果返回到二级页面，确保 TabBar 隐藏
                if let tabBarController = tabBarController as? CustomTabBarController {
                    DispatchQueue.main.async {
                        tabBarController.hideTabBar(animated: false)
                        print("[VideoDisplay] 返回到二级页面，确保 TabBar 隐藏")
                    }
                }
            }
        }

        print("[VideoDisplay] 视频中心页面消失，暂停所有播放器")
    }
    
    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        
        // 如果是被移除或退出（而非临时被其他视图覆盖），则释放所有资源
        if isMovingFromParent || isBeingDismissed {
            // 释放所有页面的播放器资源
            for page in videoPages {
                if let videoPage = page as? VideoPage {
                    videoPage.stopAndReleasePlayer()
                }
            }
            print("[VideoDisplay] 视频中心页面退出，释放所有播放器资源")
        }
    }
    
    deinit {
        // 确保所有视频页面的播放器都被正确释放
        for page in videoPages {
            // 停止播放
            if let videoPage = page as? VideoPage {
                videoPage.stopAndReleasePlayer()
            }
        }
        videoPages.removeAll()
        print("[VideoDisplay] VideoDisplayCenterViewController 已释放，所有播放器资源已清理")
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        // 设置页面控制器代理
        ui.pageViewController.dataSource = self
        ui.pageViewController.delegate = self
        
        // 设置背景色为黑色
        view.backgroundColor = .black
        ui.pageViewController.view.backgroundColor = .black
        
        // 设置UI
        ui.setupUI(
            in: self,
            showCustomNavBar: showCustomNavBar,
            hideNavBackButton: hideNavBackButton,
            needsTabBarOffset: needsTabBarOffset,
            backAction: #selector(backButtonTapped),
            searchAction: #selector(searchButtonTapped),
            moreAction: #selector(moreButtonTapped)
        )
    }
    
    // MARK: - Video Stream
    private func fetchVideoStream() {
        // 如果正在加载或没有更多数据，则不再加载
        if isLoadingMore || !hasMoreData {
            print("[VideoDisplay] 跳过加载视频流：isLoadingMore=\(isLoadingMore), hasMoreData=\(hasMoreData)")
            return
        }

        // 根据视频类型决定loadNumber
        let loadNumber: Int
        if videoListType == 4 {
            // 同城流：后端修改了加载方案，固定加载10条
            loadNumber = 10
        } else if videoListType == 1 {
            // 关注流：固定加载10条（与推荐流保持一致）
            loadNumber = 10
        } else if videoListType == 3 {
            // 朋友流：固定加载10条
            loadNumber = 10
        } else {
            // 推荐流：去掉本地页码缓存计算，一直请求10条，请求不到=没有更多（全部由后端计算）
            loadNumber = 10
        }

        print("[VideoDisplay] 开始获取视频流，类型: \(videoListType), 当前列表数量: \(videoList.count), loadNumber: \(loadNumber)")
        isLoadingMore = true

        // 记录是否是首次加载（使用类属性而非局部变量，防止页码重置导致重复"首次加载"）
        let isInitialLoad = !hasInitiallyLoaded

        // 根据视频类型调用不同的接口
        switch videoListType {
        case 1: // 关注流
            // 获取最后一条视频的信息用于翻页
            let lastVideo = isInitialLoad ? nil : videoList.last
            let createTime = lastVideo?.createTime
            let fromId = lastVideo?.id

            APIManager.shared.getFollowWorksList(size: loadNumber, createTime: createTime, fromId: fromId) { [weak self] result in
                self?.handleVideoStreamResult(result, loadNumber: loadNumber, isInitialLoad: isInitialLoad)
            }
        case 3: // 朋友流
            // 获取最后一条视频的信息用于翻页
            let lastVideo = isInitialLoad ? nil : videoList.last
            let createTime = lastVideo?.createTime
            let fromId = lastVideo?.id

            APIManager.shared.getFriendWorksList(size: loadNumber, createTime: createTime, fromId: fromId) { [weak self] result in
                self?.handleVideoStreamResult(result, loadNumber: loadNumber, isInitialLoad: isInitialLoad)
            }
        case 4: // 同城流
            let areaCodeToUse = areaCode ?? ""
            if AuthManager.shared.isLoggedIn {
                APIManager.shared.getCityWorksList(loadNumber: loadNumber, areaCode: areaCodeToUse) { [weak self] result in
                    self?.handleVideoStreamResult(result, loadNumber: loadNumber, isInitialLoad: isInitialLoad)
                }
            } else {
                let deviceId = UIDevice.current.identifierForVendor?.uuidString ?? ""
                APIManager.shared.getMainCityWorksList(deviceId: deviceId, loadNumber: loadNumber, areaCode: areaCodeToUse) { [weak self] result in
                    self?.handleVideoStreamResult(result, loadNumber: loadNumber, isInitialLoad: isInitialLoad)
                }
            }
        default: // 推荐流
            APIManager.shared.getVideoStream(loadNumber: loadNumber) { [weak self] result in
                guard let self = self else { return }
                self.isLoadingMore = false

                switch result {
                case .success(let response):
                    // 根据实际API返回结构，data直接是数组
                    if let list = response.data, !list.isEmpty {
                        self.handleSuccessfulVideoLoad(list, loadNumber: loadNumber, isInitialLoad: isInitialLoad)
                    } else {
                        print("[VideoDisplay] 获取推荐视频流成功但列表为空")
                        self.hasMoreData = false
                        if isInitialLoad {
                            self.setupInitialPages()
                        }
                    }
                case .failure(let error):
                    print("[VideoDisplay] Failed to fetch video stream: \(error.localizedDescription)")
                    if isInitialLoad {
                        self.setupInitialPages()
                    }
                }
            }
        }
    }

    // MARK: - 视频特殊处理
    /// 设置初始视频并加载更多视频拼接（支持推荐流和同城流）
    private func setupInitialVideoItemAndLoadMore(listType: VideoListLoadType) {
        guard let item = videoItem else {
            let typeName = listType == .recommend ? "推荐" : "同城"
            print("[VideoDisplay] \(typeName)模式：没有传入的videoItem，回退到普通加载")
            fetchVideoStream()
            return
        }

        let typeName: String
        switch listType {
        case .recommend: typeName = "推荐"
        case .follow: typeName = "关注"
        case .friend: typeName = "朋友"
        case .city: typeName = "同城"
        }

        print("[VideoDisplay] \(typeName)模式：设置初始视频并加载更多\(typeName)视频")
        print("[VideoDisplay] \(typeName)模式：传入视频 - \(item.worksTitle ?? "未知标题"), ID: \(item.id ?? 0)")

        // 1. 先将传入的视频添加到列表
        videoList = [item]
        print("[VideoDisplay] \(typeName)模式：初始视频列表设置完成，count: \(videoList.count)")

        // 2. 设置初始页面（显示传入的视频）
        setupInitialPages()

        // 3. 立即加载更多视频拼接在后面
        switch listType {
        case .recommend:
            loadMoreRecommendVideos()
        case .follow:
            loadMoreFollowVideos()
        case .friend:
            loadMoreFriendVideos()
        case .city:
            loadMoreCityVideos()
        }
    }

    /// 加载更多推荐视频（专门用于拼接）
    private func loadMoreRecommendVideos() {
        // 如果正在加载，则不再加载
        if isLoadingMore {
            print("[VideoDisplay] 推荐模式：正在加载中，跳过")
            return
        }

        let loadNumber = 10 // 固定加载10条

        print("[VideoDisplay] 推荐模式：开始加载更多推荐视频")
        isLoadingMore = true

        APIManager.shared.getVideoStream(loadNumber: loadNumber) { [weak self] result in
            self?.handleRecommendVideoLoadResult(result, loadNumber: loadNumber)
        }
    }

    /// 加载更多关注视频（专门用于拼接）
    private func loadMoreFollowVideos() {
        // 如果正在加载，则不再加载
        if isLoadingMore {
            print("[VideoDisplay] 关注模式：正在加载中，跳过")
            return
        }

        let size = 10 // 固定加载10条

        // 获取最后一条视频的信息用于翻页
        let lastVideo = videoList.last
        let createTime = lastVideo?.createTime
        let fromId = lastVideo?.id

        print("[VideoDisplay] 关注模式：开始加载更多关注视频")
        isLoadingMore = true

        APIManager.shared.getFollowWorksList(size: size, createTime: createTime, fromId: fromId) { [weak self] result in
            self?.handleFollowVideoLoadResult(result, loadNumber: size)
        }
    }

    /// 加载更多朋友视频（专门用于拼接）
    private func loadMoreFriendVideos() {
        // 如果正在加载，则不再加载
        if isLoadingMore {
            print("[VideoDisplay] 朋友模式：正在加载中，跳过")
            return
        }

        let size = 10 // 固定加载10条

        // 获取最后一条视频的信息用于翻页
        let lastVideo = videoList.last
        let createTime = lastVideo?.createTime
        let fromId = lastVideo?.id

        print("[VideoDisplay] 朋友模式：开始加载更多朋友视频")
        isLoadingMore = true

        APIManager.shared.getFriendWorksList(size: size, createTime: createTime, fromId: fromId) { [weak self] result in
            self?.handleFriendVideoLoadResult(result, loadNumber: size)
        }
    }

    /// 加载更多同城视频（专门用于拼接）
    private func loadMoreCityVideos() {
        // 如果正在加载，则不再加载
        if isLoadingMore {
            print("[VideoDisplay] 同城模式：正在加载中，跳过")
            return
        }

        let loadNumber = 10 // 固定加载10条
        let areaCodeToUse = areaCode ?? ""

        print("[VideoDisplay] 同城模式：开始加载更多视频，areaCode: \(areaCodeToUse)")
        isLoadingMore = true

        if AuthManager.shared.isLoggedIn {
            APIManager.shared.getCityWorksList(loadNumber: loadNumber, areaCode: areaCodeToUse) { [weak self] result in
                self?.handleCityVideoLoadResult(result, loadNumber: loadNumber)
            }
        } else {
            let deviceId = UIDevice.current.identifierForVendor?.uuidString ?? ""
            APIManager.shared.getMainCityWorksList(deviceId: deviceId, loadNumber: loadNumber, areaCode: areaCodeToUse) { [weak self] result in
                self?.handleCityVideoLoadResult(result, loadNumber: loadNumber)
            }
        }
    }

    /// 处理朋友视频加载结果（专门用于拼接）
    private func handleFriendVideoLoadResult(_ result: Result<VideoArrayResponse, APIError>, loadNumber: Int) {
        DispatchQueue.main.async {
            self.isLoadingMore = false

            switch result {
            case .success(let response):
                guard let list = response.data, !list.isEmpty else {
                    print("[VideoDisplay] 朋友模式：API返回空列表，没有更多数据")
                    self.hasMoreData = false
                    self.hasInitiallyLoaded = true
                    // 如果朋友视频列表为空，显示朋友空白占位
                    if self.videoListType == 3 && self.videoList.count <= 1 {
                        self.showFriendEmptyPlaceholder()
                    }
                    return
                }

                print("[VideoDisplay] 朋友模式：API返回 \(list.count) 个视频")

                // 过滤掉与当前正在播放的视频相同的项目
                let currentVideoId = self.videoItem?.id
                let filteredList = list.filter { $0.id != currentVideoId }
                print("[VideoDisplay] 朋友模式：过滤掉当前视频后剩余 \(filteredList.count) 个视频")

                // 添加到现有列表，避免重复
                var addedCount = 0
                for item in filteredList {
                    if !self.videoList.contains(where: { $0.id == item.id }) {
                        self.videoList.append(item)
                        addedCount += 1
                        print("[VideoDisplay] 朋友模式：添加视频 - \(item.worksTitle ?? "未知标题"), ID: \(item.id ?? 0)")
                    }
                }

                print("[VideoDisplay] 朋友模式：实际添加了 \(addedCount) 个视频，总视频数: \(self.videoList.count)")

                // 根据返回数据判断是否还有更多
                self.hasMoreData = !list.isEmpty

                // 标记为已完成首次加载
                self.hasInitiallyLoaded = true

                // 如果有朋友视频数据，隐藏占位视图
                if self.videoListType == 3 && !self.videoList.isEmpty {
                    self.hideFriendEmptyPlaceholder()
                }

            case .failure(let error):
                print("[VideoDisplay] 朋友模式：网络错误 - \(error.localizedDescription)")
                self.hasInitiallyLoaded = true
                self.hasMoreData = false
                // 如果朋友视频加载失败且只有初始视频，显示朋友空白占位
                if self.videoListType == 3 && self.videoList.count <= 1 {
                    self.showFriendEmptyPlaceholder()
                }
            }
        }
    }

    /// 处理关注视频加载结果（专门用于拼接）
    private func handleFollowVideoLoadResult(_ result: Result<VideoArrayResponse, APIError>, loadNumber: Int) {
        DispatchQueue.main.async {
            self.isLoadingMore = false

            switch result {
            case .success(let response):
                guard let list = response.data, !list.isEmpty else {
                    print("[VideoDisplay] 关注模式：API返回空列表，没有更多数据")
                    self.hasMoreData = false
                    self.hasInitiallyLoaded = true
                    return
                }

                print("[VideoDisplay] 关注模式：API返回 \(list.count) 个视频")

                // 过滤掉与当前正在播放的视频相同的项目
                let currentVideoId = self.videoItem?.id
                let filteredList = list.filter { $0.id != currentVideoId }
                print("[VideoDisplay] 关注模式：过滤掉当前视频后剩余 \(filteredList.count) 个视频")

                // 添加到现有列表，避免重复
                var addedCount = 0
                for item in filteredList {
                    if !self.videoList.contains(where: { $0.id == item.id }) {
                        self.videoList.append(item)
                        addedCount += 1
                        print("[VideoDisplay] 关注模式：添加视频 - \(item.worksTitle ?? "未知标题"), ID: \(item.id ?? 0)")
                    }
                }

                print("[VideoDisplay] 关注模式：实际添加了 \(addedCount) 个视频，总视频数: \(self.videoList.count)")

                // 根据返回数据判断是否还有更多
                self.hasMoreData = !list.isEmpty

                // 标记为已完成首次加载
                self.hasInitiallyLoaded = true

            case .failure(let error):
                print("[VideoDisplay] 关注模式：网络错误 - \(error.localizedDescription)")
                self.hasInitiallyLoaded = true
                self.hasMoreData = false
            }
        }
    }

    /// 处理推荐视频加载结果（专门用于拼接）
    private func handleRecommendVideoLoadResult(_ result: Result<VideoArrayResponse, APIError>, loadNumber: Int) {
        DispatchQueue.main.async {
            self.isLoadingMore = false

            switch result {
            case .success(let response):
                guard let list = response.data, !list.isEmpty else {
                    print("[VideoDisplay] 推荐模式：API返回空列表，没有更多数据")
                    self.hasMoreData = false
                    self.hasInitiallyLoaded = true
                    return
                }

                print("[VideoDisplay] 推荐模式：API返回 \(list.count) 个视频")

                // 过滤掉与当前正在播放的视频相同的项目
                let currentVideoId = self.videoItem?.id
                let filteredList = list.filter { $0.id != currentVideoId }
                print("[VideoDisplay] 推荐模式：过滤掉当前视频后剩余 \(filteredList.count) 个视频")

                // 添加到现有列表，避免重复
                var addedCount = 0
                for item in filteredList {
                    if !self.videoList.contains(where: { $0.id == item.id }) {
                        self.videoList.append(item)
                        addedCount += 1
                        print("[VideoDisplay] 推荐模式：添加视频 - \(item.worksTitle ?? "未知标题"), ID: \(item.id ?? 0)")
                    }
                }

                print("[VideoDisplay] 推荐模式：实际添加了 \(addedCount) 个视频，总视频数: \(self.videoList.count)")

                // 根据返回数据判断是否还有更多
                self.hasMoreData = !list.isEmpty

                // 标记为已完成首次加载
                self.hasInitiallyLoaded = true

            case .failure(let error):
                print("[VideoDisplay] 推荐模式：网络错误 - \(error.localizedDescription)")
                self.hasInitiallyLoaded = true
                self.hasMoreData = false
            }
        }
    }

    /// 处理同城视频加载结果（专门用于拼接）
    private func handleCityVideoLoadResult(_ result: Result<VideoArrayResponse, APIError>, loadNumber: Int) {
        DispatchQueue.main.async {
            self.isLoadingMore = false

            switch result {
            case .success(let response):
                guard response.isSuccess, let list = response.data else {
                    print("[VideoDisplay] 同城模式：API失败 - \(response.displayMessage)")
                    // 即使加载失败，也要标记为已完成首次加载，避免影响播放
                    self.hasInitiallyLoaded = true
                    return
                }

                print("[VideoDisplay] 同城模式：API返回 \(list.count) 个视频")

                if !list.isEmpty {
                    print("[VideoDisplay] 同城模式：开始拼接视频到列表")

                    // 由于后台改成无限刷新（重复视频随机出），我们允许重复视频
                    // 只过滤掉与当前正在播放的视频完全相同的项目
                    let currentVideoId = self.videoItem?.id
                    let filteredList = list.filter { $0.id != currentVideoId }
                    print("[VideoDisplay] 同城模式：过滤掉当前视频后剩余 \(filteredList.count) 个视频")

                    // 直接拼接到现有列表，允许重复视频
                    var addedCount = 0
                    for item in filteredList {
                        self.videoList.append(item)
                        addedCount += 1
                        print("[VideoDisplay] 同城模式：添加视频 - \(item.worksTitle ?? "未知标题"), ID: \(item.id ?? 0)")
                    }

                    print("[VideoDisplay] 同城模式：实际添加了 \(addedCount) 个视频，总视频数: \(self.videoList.count)")

                    // 根据后端逻辑：返回空列表=没有更多数据，返回非空列表=可能还有更多数据
                    self.hasMoreData = true

                    // 如果添加了新视频，通知可以继续滑动
                    if addedCount > 0 {
                        print("[VideoDisplay] 同城模式：新增了 \(addedCount) 个视频，用户可以继续滑动")
                    }
                } else {
                    print("[VideoDisplay] 同城模式：加载的视频列表为空，没有更多数据")
                    self.hasMoreData = false
                }

                // 标记为已完成首次加载
                self.hasInitiallyLoaded = true

            case .failure(let error):
                print("[VideoDisplay] 同城模式：网络错误 - \(error.localizedDescription)")
                // 即使加载失败，也要标记为已完成首次加载，避免影响播放
                self.hasInitiallyLoaded = true
                self.hasMoreData = false
            }
        }
    }

    // MARK: - Video Stream Result Handling
    /// 统一处理关注流和同城流的返回结果
    private func handleVideoStreamResult(_ result: Result<VideoArrayResponse, APIError>, loadNumber: Int, isInitialLoad: Bool) {
        DispatchQueue.main.async {
            self.isLoadingMore = false

            switch result {
            case .success(let response):
                guard response.isSuccess, let list = response.data else {
                    print("[VideoDisplay] API失败 - \(response.displayMessage)")
                    if isInitialLoad {
                        self.setupInitialPages()
                    }
                    return
                }

                if !list.isEmpty {
                    self.handleSuccessfulVideoLoad(list, loadNumber: loadNumber, isInitialLoad: isInitialLoad)
                } else {
                    print("[VideoDisplay] 获取视频流成功但列表为空")
                    self.hasMoreData = false
                    if isInitialLoad {
                        self.setupInitialPages()
                    }
                }

            case .failure(let error):
                print("[VideoDisplay] 网络错误 - \(error.localizedDescription)")
                if isInitialLoad {
                    self.setupInitialPages()
                }
            }
        }
    }

    /// 处理成功加载的视频数据
    private func handleSuccessfulVideoLoad(_ list: [VideoItem], loadNumber: Int, isInitialLoad: Bool) {
        print("[VideoDisplay] 成功获取视频流，数量: \(list.count)")

        // 打印每个视频的isMyWorks参数值
        for (index, video) in list.enumerated() {
            let title = video.worksTitle ?? "未知标题"
            let videoId = video.id != nil ? String(video.id!) : "未知ID"
            let isMyWorksValue = video.isMyWorks ?? false
            print("[VideoDisplay] 视频[\(index)] - 标题: \(title), ID: \(videoId), isMyWorks: \(isMyWorksValue)")
        }

        // 合并视频列表
        self.processVideoList(list)

        // 更新是否有更多数据
        if videoListType == 4 || videoListType == 0 {
            // 同城流和推荐流：当返回的数据为空时，表示已经刷完了（全部由后端计算）
            self.hasMoreData = !list.isEmpty
        } else {
            // 关注流：根据返回数量判断
            self.hasMoreData = list.count >= loadNumber
        }

        // 如果是首次加载成功，标记为已完成首次加载
        if isInitialLoad {
            self.hasInitiallyLoaded = true
            print("[VideoDisplay] 首次加载视频流，设置初始页面")
            self.setupInitialPages()
        } else {
            print("[VideoDisplay] 加载更多视频流，不重新设置页面")
        }
    }

    private func processVideoList(_ newList: [VideoItem]) {
        // 如果有传入的videoItem，将其作为第一个元素
        if let item = videoItem {
            // 打印传入的videoItem的isMyWorks参数值
            let title = item.worksTitle ?? "未知标题"
            let videoId = item.id != nil ? String(item.id!) : "未知ID"
            let isMyWorksValue = item.isMyWorks ?? false
            print("[VideoDisplay] 传入的videoItem - 标题: \(title), ID: \(videoId), isMyWorks: \(isMyWorksValue)")

            // 检查是否已经存在相同ID的视频，避免重复
            if !videoList.contains(where: { $0.id == item.id }) {
                videoList.insert(item, at: 0)
                print("[VideoDisplay] 将传入的videoItem插入到列表开头")
            } else {
                print("[VideoDisplay] 传入的videoItem已存在于列表中，跳过插入")
            }
        }

        // 添加新获取的视频
        if videoListType == 4 {
            // 同城模式：允许重复视频（后台改成无限刷新）
            print("[VideoDisplay] 同城模式：直接添加 \(newList.count) 个视频，允许重复")
            for item in newList {
                videoList.append(item)
            }
        } else {
            // 其他模式：避免重复
            for item in newList {
                if !videoList.contains(where: { $0.id == item.id }) {
                    videoList.append(item)
                }
            }
        }

        // 如果是朋友页面且有视频数据，隐藏占位视图
        if videoListType == 3 && !videoList.isEmpty {
            hideFriendEmptyPlaceholder()
        }

        print("[VideoDisplay] Video list updated, total count: \(videoList.count)")
    }

    // 加载更多视频
    private func loadMoreVideosIfNeeded() {
        // 确保至少有一些视频才考虑加载更多，避免在初始化阶段重复加载
        guard videoList.count > 0 && hasInitiallyLoaded else {
            print("[VideoDisplay] 跳过加载更多：videoList.count=\(videoList.count), hasInitiallyLoaded=\(hasInitiallyLoaded)")
            return
        }

        // 如果当前索引接近列表末尾，加载更多视频
        // 确保至少有3个视频才考虑"接近末尾"的逻辑，避免在初始阶段频繁触发
        let triggerIndex = max(videoList.count - 3, 2)
        if currentIndex >= triggerIndex && hasMoreData && !isLoadingMore {
            print("[VideoDisplay] 当前索引 \(currentIndex) 接近列表末尾（触发点: \(triggerIndex)），加载更多视频")

            if videoListType == 4 {
                // 同城模式：使用专门的同城视频加载方法
                loadMoreCityVideos()
            } else if videoListType == 0 && videoItem != nil {
                // 推荐流拼接模式：使用专门的推荐视频加载方法
                loadMoreRecommendVideos()
            } else if videoListType == 1 && videoItem != nil {
                // 关注流拼接模式：使用专门的关注视频加载方法
                loadMoreFollowVideos()
            } else if videoListType == 3 && videoItem != nil {
                // 朋友流拼接模式：使用专门的朋友视频加载方法
                loadMoreFriendVideos()
            } else {
                // 其他模式：使用通用的视频流加载方法
                fetchVideoStream()
            }
        }
    }

    private func setupInitialPages() {
        print("[VideoDisplay] 开始设置初始页面")
        // 释放之前的所有页面资源
        for page in videoPages {
            if let videoPage = page as? VideoPage {
                videoPage.stopAndReleasePlayer()
                print("[VideoDisplay] 重新设置初始页面，释放页面 \(videoPage.pageIndex) 的资源")
            }
        }
        videoPages.removeAll()
        
        // 添加日志，帮助排查重复创建问题
        print("[VideoDisplay] 设置初始页面，当前视频列表数量: \(videoList.count), 是否有初始视频项: \(videoItem != nil)")
        
        // 如果视频列表为空但有传入的videoItem，直接创建一个页面
        if videoList.isEmpty && videoItem != nil {
            let page = VideoPage(index: 0, videoItem: videoItem)
            page.delegate = self
            // 设置预览模式状态
            page.setPreviewMode(videoListType == -2 || videoListType == -3)
            videoPages = [page]
            print("[VideoDisplay] 创建单个初始页面，索引: 0")
        } else if !videoList.isEmpty {
            // 创建初始页面 - 确保包含 initialIndex
            let startTime = Date()

            // 计算需要创建的页面范围，确保包含 initialIndex
            let targetIndex = min(initialIndex, videoList.count - 1)
            let startIndex = max(0, targetIndex - 1) // 从目标索引前1个开始
            let endIndex = min(startIndex + pageSize, videoList.count) // 创建 pageSize 个页面

            print("[VideoDisplay] 创建页面范围: \(startIndex)..<\(endIndex), 目标索引: \(targetIndex)")

            videoPages = (startIndex..<endIndex).map { index -> VideoPage in
                let page = VideoPage(index: index, videoItem: videoList[index])
                page.delegate = self
                // 设置预览模式状态
                page.setPreviewMode(videoListType == -2 || videoListType == -3)
                print("[VideoDisplay] 批量创建初始页面，索引: \(index)")
                return page
            }
            print("[VideoDisplay] 批量创建 \(videoPages.count) 个页面，耗时: \(Date().timeIntervalSince(startTime))秒")
        } else {
            // 没有视频数据，检查是否是朋友页面
            if videoListType == 3 {
                // 朋友页面显示占位视图
                showFriendEmptyPlaceholder()
                print("[VideoDisplay] 朋友页面无视频数据，显示占位视图")
                return
            } else {
                // 其他页面创建空页面
                let page = VideoPage(index: 0)
                page.delegate = self
                // 设置预览模式状态
                page.setPreviewMode(videoListType == -2 || videoListType == -3)
                videoPages = [page]
                print("[VideoDisplay] 创建空页面，索引: 0")
            }
        }
        
        // 设置初始页面
        if let firstPage = videoPages.first {
            ui.pageViewController.setViewControllers(
                [firstPage],
                direction: .forward,
                animated: false
            )
            print("[VideoDisplay] 设置初始控制器页面，索引: \(firstPage.pageIndex)")
        }
        
        // 设置当前索引：外部指定优先，否则默认0
        if let _ = providedVideoList {
            currentIndex = min(initialIndex, max(0, videoList.count - 1))

            // 查找对应 currentIndex 的页面
            if let targetPage = videoPages.first(where: { $0.pageIndex == currentIndex }) {
                ui.pageViewController.setViewControllers([targetPage], direction: .forward, animated: false)
                // 重要：更新当前的videoItem为对应索引的视频数据
                self.videoItem = targetPage.videoItem
                print("[VideoDisplay] 跳转到索引 \(currentIndex)，更新videoItem为: \(targetPage.videoItem?.worksTitle ?? "未知")")

                // 确保初始页面加载评论数量
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    targetPage.loadCommentCount()
                    print("[VideoDisplay] 为初始页面手动触发评论数量加载（延迟0.3秒）")
                }
            } else {
                // 如果没有找到对应的页面，使用第一个页面并更新 currentIndex
                if let firstPage = videoPages.first {
                    currentIndex = firstPage.pageIndex
                    self.videoItem = firstPage.videoItem
                    print("[VideoDisplay] 未找到目标页面，使用第一个页面，索引: \(currentIndex)，videoItem: \(firstPage.videoItem?.worksTitle ?? "未知")")

                    // 确保初始页面加载评论数量
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        firstPage.loadCommentCount()
                        print("[VideoDisplay] 为初始页面手动触发评论数量加载（延迟0.3秒）")
                    }
                }
            }
        } else {
            currentIndex = 0
            // 确保初始页面加载评论数量
            if let firstPage = videoPages.first {
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    firstPage.loadCommentCount()
                    print("[VideoDisplay] 为初始页面手动触发评论数量加载（延迟0.3秒）")
                }
            }
        }
        print("[VideoDisplay] 初始页面设置完成，当前索引: \(currentIndex)")

        // 为初始显示的视频添加观看记录
        if currentIndex < videoPages.count {
            let currentPage = videoPages[currentIndex]
            if let videoPage = currentPage as? VideoPage {
                print("[VideoDisplay] 为初始显示的视频添加观看记录")
                addWatchRecord(for: videoPage.videoItem)
            }
        }
    }
    
    // MARK: - Page Management
    private func getPage(at index: Int) -> VideoPage? {
        return videoPages.first { $0.pageIndex == index }
    }
    
    private func createNewPage(at index: Int) -> VideoPage {
        // 如果该索引的页面已存在，则直接返回，避免创建重复页面
        if let existing = getPage(at: index) {
            return existing
        }
        // 检查索引是否在视频列表范围内
        let videoItem = index < videoList.count ? videoList[index] : nil

        let page = VideoPage(index: index, videoItem: videoItem)
        page.delegate = self
        // 设置预览模式状态
        page.setPreviewMode(videoListType == -2 || videoListType == -3)
        videoPages.append(page)

        // 打印调试信息
        if let item = videoItem {
            print("[VideoDisplay] Creating page at index \(index) with video: \(item.worksTitle ?? "未知标题"), ID: \(item.id ?? 0)")
        } else {
            print("[VideoDisplay] Creating page at index \(index) with nil videoItem (videoList.count: \(videoList.count))")
        }
        
        // 清理不需要的页面
        cleanupPages()
        
        return page
    }
    
    // 重置到顶部页面，释放所有其他页面资源
    private func resetToTopPage() {
        // 停止并释放所有页面的播放器资源
        for page in videoPages {
            if let videoPage = page as? VideoPage {
                videoPage.stopAndReleasePlayer()
            }
        }
        
        // 清空页面数组
        videoPages.removeAll()
        
        // 重新创建顶部页面
        if !videoList.isEmpty {
            let topPage = VideoPage(index: 0, videoItem: videoList.first)
            topPage.delegate = self
            // 设置预览模式状态
            topPage.setPreviewMode(videoListType == -2 || videoListType == -3)
            videoPages = [topPage]
            
            // 设置为当前页面
            ui.pageViewController.setViewControllers(
                [topPage],
                direction: .forward,
                animated: false
            )
            
            // 重置当前索引
            currentIndex = 0
            
            print("[VideoDisplay] 已重置到顶部页面，释放所有播放器资源")
        }
    }
    
    private func cleanupPages() {
        // 短视频应用资源管理机制：滑到第三个才释放第一个
        // 保持前后各两页的缓存，即当前页面的前两个和后两个
        let visibleIndices = (currentIndex - 2...currentIndex + 2)
        
        // 找出需要移除的页面
        let pagesToRemove = videoPages.filter { !visibleIndices.contains($0.pageIndex) }
        
        // 释放这些页面的播放器资源
        for page in pagesToRemove {
            if let videoPage = page as? VideoPage {
                videoPage.stopAndReleasePlayer()
                print("[VideoDisplay] 清理页面索引 \(videoPage.pageIndex) 的播放器资源")
            }
        }
        
        // 从数组中移除这些页面
        videoPages = videoPages.filter { visibleIndices.contains($0.pageIndex) }
    }
    
    // MARK: - Actions
    @objc private func backButtonTapped() {
        if let navigationController = navigationController {
            navigationController.popViewController(animated: true)
        } else {
            dismiss(animated: true)
        }
    }
    
    @objc private func searchButtonTapped() {
        print("搜索按钮被点击")

        // 创建搜索页面
        let searchVC = SearchViewController()

        // 根据当前的展示方式选择不同的跳转方式
        if let navigationController = navigationController {
            // 如果有导航控制器，使用push方式
            navigationController.pushViewController(searchVC, animated: true)
        } else {
            // 如果是模态展示，创建新的导航控制器
            let navController = UINavigationController(rootViewController: searchVC)
            navController.modalPresentationStyle = .fullScreen
            present(navController, animated: true)
        }
    }
    
    // MARK: - Share Sheet
    @objc func showShareSheet() {
        // 获取当前播放的视频信息
        guard currentIndex < videoList.count else {
            print("[VideoShare] 当前索引超出视频列表范围")
            return
        }

        let currentVideo = videoList[currentIndex]

        // 构建视频标题，最多10个字，超出截取
        let videoTitle = currentVideo.worksTitle ?? "精彩视频"
        let truncatedTitle = String(videoTitle.prefix(10))
        let finalTitle = truncatedTitle.count < videoTitle.count ? truncatedTitle + "..." : truncatedTitle

        // 构建分享文案
        let shareText = "复制打开【树小柒】APP，看看《\(finalTitle)》"

        // 构建分享链接
        let videoId = currentVideo.worksIdString ?? String(currentIndex)
        let shareLink = "https://yigou.gzyoushu.com/#/sxqVideo/videoDetail/videoDetail?id=\(videoId)"

        print("[VideoShare] 准备分享视频 - 标题: \(finalTitle), ID: \(videoId), 链接: \(shareLink)")

        let payload = SharePayload(
            title: shareText,
            description: shareText,
            link: shareLink,
            thumbnail: currentVideo.fullCoverImageURL,
            image: nil,
            type: .video(id: videoId, link: shareLink),
            extras: [
                "pageIndex": currentIndex,
                "videoItem": currentVideo,
                "shareText": shareText,
                "videoDisplayController": self // 添加对当前控制器的引用
            ]
        )
        NotificationCenter.default.post(name: .shareRequested, object: payload)
    }
    
    @objc private func moreButtonTapped() {
        showShareSheet()
    }

    // MARK: - 侧滑返回手势
    private func setupSwipeBackGesture() {
        // 创建侧滑手势识别器
        panGestureRecognizer = UIPanGestureRecognizer(target: self, action: #selector(handlePanGesture(_:)))
        panGestureRecognizer?.delegate = self
        view.addGestureRecognizer(panGestureRecognizer!)

        print("[VideoDisplay] 侧滑返回手势已设置")
    }

    @objc private func handlePanGesture(_ gesture: UIPanGestureRecognizer) {
        let translation = gesture.translation(in: view)
        let velocity = gesture.velocity(in: view)
        let progress = translation.x / view.bounds.width

        switch gesture.state {
        case .began:
            // 只有从左边缘开始的右滑手势才触发返回
            let startLocation = gesture.location(in: view)
            if startLocation.x < 50 && velocity.x > 0 { // 从左边缘50px内开始且向右滑动
                interactiveTransition = UIPercentDrivenInteractiveTransition()

                // 根据当前的展示方式选择不同的返回方式
                if let navigationController = navigationController {
                    navigationController.popViewController(animated: true)
                } else {
                    dismiss(animated: true)
                }
                print("[VideoDisplay] 开始侧滑返回手势")
            }

        case .changed:
            guard let transition = interactiveTransition else { return }
            let clampedProgress = max(0, min(1, progress))
            transition.update(clampedProgress)

        case .ended, .cancelled:
            guard let transition = interactiveTransition else { return }

            // 根据滑动距离和速度决定是否完成返回
            let shouldComplete = progress > 0.3 || velocity.x > 1000

            if shouldComplete {
                transition.finish()
                print("[VideoDisplay] 侧滑返回完成")
            } else {
                transition.cancel()
                print("[VideoDisplay] 侧滑返回取消")
            }

            interactiveTransition = nil

        default:
            break
        }
    }
}

// MARK: - UIGestureRecognizerDelegate
extension VideoDisplayCenterViewController: UIGestureRecognizerDelegate {
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldRecognizeSimultaneouslyWith otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        // 允许侧滑手势与页面滑动手势同时识别
        if gestureRecognizer == panGestureRecognizer {
            return true
        }
        return false
    }

    func gestureRecognizerShouldBegin(_ gestureRecognizer: UIGestureRecognizer) -> Bool {
        // 只有侧滑手势需要特殊处理
        if gestureRecognizer == panGestureRecognizer {
            guard let panGesture = gestureRecognizer as? UIPanGestureRecognizer else { return false }

            let velocity = panGesture.velocity(in: view)
            let location = panGesture.location(in: view)

            // 只有从左边缘开始的水平右滑手势才触发
            let isFromLeftEdge = location.x < 50
            let isHorizontalSwipe = abs(velocity.x) > abs(velocity.y)
            let isRightSwipe = velocity.x > 0

            return isFromLeftEdge && isHorizontalSwipe && isRightSwipe
        }

        return true
    }
}

// MARK: - VideoPageDelegate
extension VideoDisplayCenterViewController: VideoPageDelegate {
    // 子页面回调
    func videoPageDidTapShare(_ page: VideoPage) {
        showShareSheet()
    }
}

// MARK: - UIPageViewControllerDataSource
extension VideoDisplayCenterViewController: UIPageViewControllerDataSource {
    func pageViewController(
        _ pageViewController: UIPageViewController,
        viewControllerBefore viewController: UIViewController
    ) -> UIViewController? {
        // 草稿预览模式和内容管理预览模式下禁用上下滚动
        if videoListType == -2 || videoListType == -3 {
            return nil
        }

        guard let currentPage = viewController as? VideoPage else { return nil }
        let previousIndex = currentPage.pageIndex - 1

        if previousIndex >= 0 {
            return getPage(at: previousIndex) ?? createNewPage(at: previousIndex)
        }
        return nil
    }
    
    func pageViewController(
        _ pageViewController: UIPageViewController,
        viewControllerAfter viewController: UIViewController
    ) -> UIViewController? {
        // 草稿预览模式和内容管理预览模式下禁用上下滚动
        if videoListType == -2 || videoListType == -3 {
            return nil
        }

        guard let currentPage = viewController as? VideoPage else { return nil }
        let nextIndex = currentPage.pageIndex + 1

        // 如果接近列表末尾，尝试加载更多
        loadMoreVideosIfNeeded()

        // 如果下一个索引超出视频列表范围
        if nextIndex >= videoList.count {
            // 如果没有更多数据，则返回nil
            if !hasMoreData {
                print("[VideoDisplay] 下一个索引 \(nextIndex) 超出范围且没有更多数据，返回nil")
                addBounceEffectIfNeeded()
                return nil
            }

            // 如果有更多数据但正在加载中，返回nil等待加载完成
            if isLoadingMore {
                print("[VideoDisplay] 下一个索引 \(nextIndex) 超出范围但正在加载更多数据，返回nil")
                return nil
            }

            // 如果有更多数据但还没开始加载，也返回nil，避免创建空页面
            print("[VideoDisplay] 下一个索引 \(nextIndex) 超出范围，等待数据加载")
            return nil
        }

        // 创建新页面（只有在有视频数据时才创建）
        return getPage(at: nextIndex) ?? createNewPage(at: nextIndex)
    }
    
    // 添加弹簧效果
    private func addBounceEffectIfNeeded() {
        // 使用弹簧动画模拟拖动效果
        let bounceAnimation = CAKeyframeAnimation(keyPath: "transform.translation.y")
        bounceAnimation.values = [0, -20, 0]
        bounceAnimation.keyTimes = [0, 0.5, 1.0]
        bounceAnimation.duration = 0.3
        bounceAnimation.timingFunctions = [
            CAMediaTimingFunction(name: .easeOut),
            CAMediaTimingFunction(name: .easeIn)
        ]
        
        ui.pageViewController.view.layer.add(bounceAnimation, forKey: "bounceAnimation")
    }
}

// MARK: - UIPageViewControllerDelegate
extension VideoDisplayCenterViewController: UIPageViewControllerDelegate {
    func pageViewController(
        _ pageViewController: UIPageViewController,
        didFinishAnimating finished: Bool,
        previousViewControllers: [UIViewController],
        transitionCompleted completed: Bool
    ) {
        if completed,
           let currentVC = pageViewController.viewControllers?.first as? VideoPage {
            // 获取之前的索引
            let previousIndex = currentIndex
            
            // 更新当前索引
            currentIndex = currentVC.pageIndex

            // 重要：更新当前的videoItem为切换后页面的视频数据
            self.videoItem = currentVC.videoItem
            print("[VideoDisplay] 页面切换完成，更新videoItem为: \(currentVC.videoItem?.worksTitle ?? "未知")")

            // 重置之前页面的评论数请求状态（防止竞态条件）
            for previousVC in previousViewControllers {
                if let previousPage = previousVC as? VideoPage {
                    previousPage.resetCommentRequestState()
                }
            }

            // 获取最新的评论数量
            currentVC.loadCommentCount()

            // 添加详细日志，帮助排查重复创建问题
            print("[VideoDisplay] 页面切换完成：从 \(previousIndex) 到 \(currentIndex)，当前页面数量: \(videoPages.count)")
            for (i, page) in videoPages.enumerated() {
                if let vp = page as? VideoPage {
                    print("[VideoDisplay] 页面[\(i)]: 索引 \(vp.pageIndex), 有播放器: \(vp.player != nil)")
                }
            }
            
            // 确保当前页面的视频播放
            if let player = currentVC.player {
                // 从头开始播放视频
                currentVC.restartPlayFromBeginning()
                print("[VideoDisplay] 当前页面 \(currentVC.pageIndex) 的视频从头开始播放")
                if let title = currentVC.videoItem?.worksTitle {
                    print("[VideoStatus] \(title) - 视频开始播放")
                }

                // 添加观看记录
                addWatchRecord(for: currentVC.videoItem)
            } else if currentVC.videoItem != nil {
                // 如果没有播放器但有视频数据，初始化播放器
                print("[VideoDisplay] 当前页面 \(currentVC.pageIndex) 没有播放器，尝试初始化")
                currentVC.initializePlayerIfNeeded()

                // 添加观看记录
                addWatchRecord(for: currentVC.videoItem)
            }
            
            // 处理之前的页面
            for previousVC in previousViewControllers {
                if let previousPage = previousVC as? VideoPage {
                    // 如果前一个页面不在可见范围内，则立即停止并释放其播放器
                    if previousPage.pageIndex != currentIndex &&
                       previousPage.pageIndex != currentIndex - 1 &&
                       previousPage.pageIndex != currentIndex + 1 {
                        previousPage.stopAndReleasePlayer()
                        print("[VideoDisplay] 页面切换完成，停止并释放页面 \(previousPage.pageIndex) 的播放器")
                        if let title = previousPage.videoItem?.worksTitle {
                            print("[VideoStatus] \(title) - 播放器已释放")
                        }
                    }
                }
            }
            
            // 清理不需要的页面
            cleanupPages()
            
            // 如果接近列表末尾，尝试加载更多
            loadMoreVideosIfNeeded()
        }
    }
    
    // 添加页面切换开始的处理
    func pageViewController(
        _ pageViewController: UIPageViewController,
        willTransitionTo pendingViewControllers: [UIViewController]
    ) {
        // 页面切换开始时，暂停当前页面的播放
        if let currentVC = pageViewController.viewControllers?.first as? VideoPage,
           let pendingVC = pendingViewControllers.first as? VideoPage {
            currentVC.player?.pause()
            print("[VideoDisplay] 页面切换开始，从 \(currentVC.pageIndex) 到 \(pendingVC.pageIndex)，暂停当前页面播放")
            if let title = currentVC.videoItem?.worksTitle {
                print("[VideoStatus] \(title) - 视频已暂停")
            }
        }
    }
}

@objcMembers
class VideoPlayerDelegate: NSObject, TXVodPlayListener {
    weak var videoPage: VideoPage?

    // 缓冲状态跟踪
    private var isBuffering = false
    private var lastBufferProgress: Float = 0.0

    init(videoPage: VideoPage) {
        self.videoPage = videoPage
        super.init()
    }
    
    func onPlayEvent(_ player: TXVodPlayer!, event EvtID: Int32, withParam param: [AnyHashable : Any]!) {
        DispatchQueue.main.async { [weak self] in
            guard let self = self, let videoPage = self.videoPage else { return }
            
            switch EvtID {
            case 2004: // 播放开始
                print("[VideoPage] 页面 \(videoPage.pageIndex) 播放开始")
                videoPage.isPlaying = true
                videoPage.ui.updatePauseButtonVisibility(isPlaying: true)

            case 2006: // 播放结束
                print("[VideoPage] 页面 \(videoPage.pageIndex) 播放结束")
                // 循环播放已在播放器配置中设置，无需额外处理

            case 2007: // 数据缓冲中
                self.isBuffering = true
                print("[VideoPage] 页面 \(videoPage.pageIndex) 开始缓冲")

            case 2014: // 视频缓冲结束
                self.isBuffering = false
                // 缓冲结束时，设置缓冲进度为100%
                self.lastBufferProgress = 1.0
                print("[VideoPage] 页面 \(videoPage.pageIndex) 缓冲结束，设置缓冲进度为100%")
                videoPage.ui.updateBufferProgress(1.0)

            case 2005: // 播放进度更新
                // 从事件参数中获取可播放时长信息
                if let playableDuration = param["EVT_PLAYABLE_DURATION"] as? NSNumber,
                   let totalDuration = param["EVT_PLAY_DURATION"] as? NSNumber {
                    let bufferProgress = totalDuration.floatValue > 0 ?
                        min(playableDuration.floatValue / totalDuration.floatValue, 1.0) : 0.0

                    // 只有当缓冲进度有显著变化时才更新UI
                    if abs(bufferProgress - self.lastBufferProgress) > 0.01 {
                        self.lastBufferProgress = bufferProgress
                        print("[VideoPage] 页面 \(videoPage.pageIndex) 缓冲进度更新: \(bufferProgress)")
                        videoPage.ui.updateBufferProgress(bufferProgress)
                    }
                }

            case -2301: // 网络断连，且经多次重连抢救无效
                print("[VideoPage] 页面 \(videoPage.pageIndex) 播放失败：网络断连")
                videoPage.showPlayError(message: "网络连接失败，请检查网络设置")

            case -2303: // 加载视频失败
                print("[VideoPage] 页面 \(videoPage.pageIndex) 播放失败：加载视频失败")
                videoPage.showPlayError(message: "视频加载失败，请稍后重试")

            case -2305: // 解码失败
                print("[VideoPage] 页面 \(videoPage.pageIndex) 播放失败：解码失败")
                videoPage.showPlayError(message: "视频解码失败，请稍后重试")

            default:
                break
            }
        }
    }
    
    func onNetStatus(_ player: TXVodPlayer!, withParam param: [AnyHashable : Any]!) {
        // 网络状态变化，可以在这里处理网络相关的UI更新
    }
}

// MARK: - VideoPage
class VideoPage: UIViewController {
    let pageIndex: Int
    var videoItem: VideoItem?
    weak var delegate: VideoPageDelegate?
    
    // 添加播放器代理
    private var playerDelegate: VideoPlayerDelegate?
    
    // 添加属性以获取父视图控制器的配置
    var needsTabBarOffset: Bool {
        return (parent as? VideoDisplayCenterViewController)?.needsTabBarOffset ?? true
    }

    // 预览模式状态（在创建时设置，避免依赖parent关系）
    private var _isPreviewMode: Bool = false

    // 设置预览模式状态
    func setPreviewMode(_ isPreview: Bool) {
        _isPreviewMode = isPreview
        print("[VideoPage] 设置预览模式状态: \(isPreview)")
    }

    // 检测是否为草稿预览模式或内容管理预览模式
    var isDraftPreviewMode: Bool {
        // 优先使用存储的状态
        if _isPreviewMode {
            print("[VideoPage] 使用存储的预览模式状态: true")
            return true
        }

        // 如果没有存储状态，尝试从parent获取
        let listType = (parent as? VideoDisplayCenterViewController)?.videoListType ?? 0
        let isPreview = listType == -2 || listType == -3
        print("[VideoPage] 从parent检查预览模式 - listType: \(listType), isPreview: \(isPreview)")
        return isPreview
    }
    
    // MARK: - Initialization
    init(index: Int, videoItem: VideoItem? = nil) {
        self.pageIndex = index
        self.videoItem = videoItem
        super.init(nibName: nil, bundle: nil)
        // 初始化播放器代理
        self.playerDelegate = VideoPlayerDelegate(videoPage: self)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Properties
    private var isLiked = false
    private var isCollected = false
    var isPlaying = true
    private var isFollowed = false
    private var hasRefreshedVideoDetail = false // 是否已经刷新过视频详情
    
    // MARK: - Player
    var player: TXVodPlayer?
    
    // 新增：笔记轮播视图
    private var noteCarouselView: NoteCarouselView?
    // 指示条已内置 NoteCarouselView
    
    // UI组件
    let ui = VideoPageUIComponents()
    
    // 添加定时器用于更新进度条
    private var progressUpdateTimer: Timer?
    
    // MARK: - 草稿预览模式空方法
    @objc private func emptyAction() {
        // 草稿预览模式下的空方法，不执行任何操作
        print("[VideoPage] 草稿预览模式：交互被禁用")
    }

    // MARK: - Lifecycle & Setup
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .black
        setupUI()

        // 初始化视频信息
        setupVideoInfo()

        // 使用公共方法初始化播放器
        initializePlayerIfNeeded()

        // 刷新视频详情（仅首次进入时）
        refreshVideoDetailIfNeeded()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        
        // 页面出现时，如果有播放器则恢复播放，否则尝试初始化播放器
        if let player = player {
            player.resume()
            isPlaying = true
            ui.updatePauseButtonVisibility(isPlaying: true)
            print("[VideoPage] 页面 \(pageIndex) 出现，恢复播放")
        } else if videoItem != nil {
            // 如果没有播放器但有视频数据，初始化播放器
            print("[VideoPage] 页面 \(pageIndex) 出现，尝试初始化播放器")
            initializePlayerIfNeeded()
        }

        // 页面出现时启动轮播
        noteCarouselView?.startAutoScroll()
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)

        // 确保评论数量已加载（防止首次进入时评论数没有及时显示）
        if videoItem != nil {
            // 延迟一点时间确保UI完全加载
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
                self?.loadCommentCount()
                print("[VideoPage] 页面 \(self?.pageIndex ?? -1) 完全显示，延迟确保评论数量已加载")
            }
        }
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)

        // 页面消失时只暂停播放，不释放资源
        // 资源释放由 cleanupPages 方法控制，或在退出时统一释放
        player?.pause()
        isPlaying = false
        print("[VideoPage] 页面 \(pageIndex) 消失，暂停播放")

        // 重置评论数请求状态（防止页面切换时的竞态条件）
        currentCommentRequestVideoId = nil
        print("[VideoPage] 页面 \(pageIndex) 消失，重置评论数请求状态")

        // 页面消失时暂停轮播
        noteCarouselView?.stopAutoScroll()
    }
    
    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        
        // 如果页面已从父视图控制器移除，释放资源
        if parent == nil {
            stopAndReleasePlayer()
            print("[VideoPage] 页面 \(pageIndex) 已从父视图移除，释放资源")
            // 释放轮播计时器
            noteCarouselView?.stopAutoScroll()
        }
    }
    
    override func didMove(toParent parent: UIViewController?) {
        super.didMove(toParent: parent)
        
        // 如果被移除，释放资源
        if parent == nil {
            stopAndReleasePlayer()
            print("[VideoPage] 页面 \(pageIndex) 被移除，释放资源")
            // 释放轮播计时器
            noteCarouselView?.stopAutoScroll()
        }
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        
        // 确保在布局完成后添加渐变效果
        ui.updateAvatarBorder()
        ui.updateFollowButtonState(isFollowed: isFollowed)
    }
    
    // MARK: - 进度条更新
    private func startProgressUpdateTimer() {
        // 停止之前的定时器
        progressUpdateTimer?.invalidate()
        
        // 创建新的定时器，每0.5秒更新一次进度条
        progressUpdateTimer = Timer.scheduledTimer(
            timeInterval: 0.5,
            target: self,
            selector: #selector(updateVideoProgress),
            userInfo: nil,
            repeats: true
        )
    }
    
    @objc private func updateVideoProgress() {
        guard let player = player, isPlaying else { return }

        // 获取视频总时长和当前播放时间
        let duration = Float(player.duration())
        let currentTime = Float(player.currentPlaybackTime())

        // 缓冲进度现在通过播放器事件来更新，这里不再计算
        // 只更新播放进度，缓冲进度由 VideoPlayerDelegate 的事件处理

        // 添加调试信息
        print("[VideoPage] 播放进度更新: 当前时间=\(currentTime), 总时长=\(duration)")

        // 更新进度条（不传递缓冲进度，缓冲进度由事件单独更新）
        ui.updateProgress(currentTime: currentTime, duration: duration, bufferProgress: 0)
    }
    
    private func setupUI() {
        // 在草稿预览模式下禁用交互
        if isDraftPreviewMode {
            ui.setupUI(
                in: self,
                commentTapAction: #selector(emptyAction), // 禁用评论点击
                videoPlayerTapAction: #selector(videoPlayerTapped), // 保留播放/暂停功能
                followButtonTapAction: #selector(emptyAction), // 禁用关注按钮
                interactionButtonTapAction: #selector(emptyAction), // 禁用点赞、收藏、分享等交互
                commentDropDownTapAction: #selector(emptyAction) // 禁用评论下拉
            )
            // 草稿预览模式下禁用头像和用户名点击
            ui.userAvatarImageView.isUserInteractionEnabled = false
            ui.userNameLabel.isUserInteractionEnabled = false
            // 设置预览模式UI状态
            ui.setPreviewModeUI(enabled: true)
            print("[VideoPage] 草稿预览模式：已禁用所有交互功能")
        } else {
            ui.setupUI(
                in: self,
                commentTapAction: #selector(commentViewTapped),
                videoPlayerTapAction: #selector(videoPlayerTapped),
                followButtonTapAction: #selector(followButtonTapped),
                interactionButtonTapAction: #selector(interactionButtonTapped(_:)),
                commentDropDownTapAction: #selector(commentDropDownTapped)
            )
            // 新增：头像和用户名点击手势
            let avatarTap = UITapGestureRecognizer(target: self, action: #selector(avatarTapped))
            ui.userAvatarImageView.isUserInteractionEnabled = true
            ui.userAvatarImageView.addGestureRecognizer(avatarTap)
            let nameTap = UITapGestureRecognizer(target: self, action: #selector(avatarTapped))
            ui.userNameLabel.isUserInteractionEnabled = true
            ui.userNameLabel.addGestureRecognizer(nameTap)
            // 设置正常模式UI状态
            ui.setPreviewModeUI(enabled: false)
        }
    }
    
    // 设置视频信息
    private func setupVideoInfo() {
        guard let item = videoItem else { return }

        // 重置评论数请求状态（防止之前的请求干扰）
        currentCommentRequestVideoId = nil

        // 打印调试信息：显示isMyWorks参数值
        let videoTitle = item.worksTitle ?? "未知标题"
        let videoId = item.id != nil ? String(item.id!) : "未知ID"
        let isMyWorksValue = item.isMyWorks ?? false
        let isMineValue = item.isMine
        print("[VideoPage] 视频信息 - 标题: \(videoTitle), ID: \(videoId), isMyWorks: \(isMyWorksValue), isMine: \(isMineValue)")

        // 在预览模式下，不修改关注按钮状态（保持预览模式的UI设置）
        if !isDraftPreviewMode {
            // 隐藏关注按钮：若作品属于当前登录用户 (后端返回 isMyWorks = true)
            if item.isMyWorks == true {
                ui.followButton.isHidden = true
                print("[VideoPage] 隐藏关注按钮 - 这是当前用户的作品")
            } else {
                ui.followButton.isHidden = false
                print("[VideoPage] 显示关注按钮 - 这不是当前用户的作品")
            }
        } else {
            print("[VideoPage] 预览模式下保持关注按钮的预览状态")
        }
        
        // 设置作者名称 - 从svUserMainVo.customerName获取
        if let userName = item.svUserMainVo?.customerName {
            ui.userNameLabel.text = userName
        }
        
        // 设置作品标题 - 从worksTitle获取
        ui.titleLabel.text = item.worksTitle
        
        // 设置头像
        if let avatarUrl = item.svUserMainVo?.wxAvator, !avatarUrl.isEmpty {
            // 如果有头像URL，加载头像
            // 这里假设有一个加载图片的方法，实际项目中可能使用Kingfisher等库
            loadAvatar(from: avatarUrl)
        } else {
            // 如果没有头像URL，使用默认头像
            ui.userAvatarImageView.image = UIImage(named: "default_avatar")
            print("[VideoPage] 使用默认头像")
        }
        
        // 设置交互按钮数字（初始值，后续会通过详情API更新）
        setupInteractionButtonCounts()

        // 获取最新的评论数量
        print("[VideoPage] setupVideoInfo 调用 loadCommentCount - 页面索引: \(pageIndex)")
        loadCommentCount()

        // 设置状态信息（初始值，后续会通过详情API更新）
        setupVideoInfoStates()
    }

    // 刷新视频详情（补全状态信息）
    private func refreshVideoDetailIfNeeded() {
        // 只在首次进入且有视频ID时执行
        guard !hasRefreshedVideoDetail,
              let videoId = videoItem?.id else {
            print("[VideoPage] 跳过视频详情刷新 - 已刷新: \(hasRefreshedVideoDetail), 视频ID: \(videoItem?.id ?? 0)")
            return
        }

        hasRefreshedVideoDetail = true
        print("[VideoPage] 开始刷新视频详情 - 视频ID: \(videoId)")

        APIManager.shared.getVideoDetail(videoId: videoId) { [weak self] result in
            guard let self = self else { return }

            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    guard let detailVideoItem = response.data else {
                        print("[VideoPage] 视频详情响应数据为空")
                        return
                    }

                    print("[VideoPage] 成功获取视频详情，开始更新状态信息")
                    self.updateVideoItemWithDetail(detailVideoItem)

                case .failure(let error):
                    print("[VideoPage] 获取视频详情失败: \(error.localizedDescription)")
                    // 失败不影响播放，只是状态信息可能不准确
                }
            }
        }
    }

    // 使用详情数据更新视频信息
    private func updateVideoItemWithDetail(_ detailItem: VideoItem) {
        // 保留原有的播放相关信息，只更新状态信息
        guard var currentItem = self.videoItem else { return }

        // 更新用户交互状态信息
        currentItem.svUserMainVo = detailItem.svUserMainVo
        currentItem.isLike = detailItem.isLike
        currentItem.isCollect = detailItem.isCollect

        // 更新数字信息（可能有变化）
        currentItem.likeNumber = detailItem.likeNumber
        currentItem.collectNumber = detailItem.collectNumber
        currentItem.commentNumber = detailItem.commentNumber
        currentItem.shareNumber = detailItem.shareNumber

        // 更新作者信息（可能更完整）
        if detailItem.svUserMainVo != nil {
            currentItem.svUserMainVo = detailItem.svUserMainVo
        }

        // 更新isMyWorks状态
        currentItem.isMyWorks = detailItem.isMyWorks

        // 保存更新后的数据
        self.videoItem = currentItem

        print("[VideoPage] 视频详情更新完成，重新设置UI状态")

        // 重新设置状态
        self.setupVideoInfoStates()

        // 重新设置交互按钮数字
        self.setupInteractionButtonCounts()
    }

    // 单独设置状态信息的方法
    private func setupVideoInfoStates() {
        guard let item = videoItem else { return }

        // 设置关注、点赞、收藏状态
        if let userInfo = item.svUserMainVo {
            isFollowed = userInfo.follow == true
            isLiked = userInfo.like == true
            isCollected = userInfo.collect == true

            print("[VideoPage] 从详情svUserMainVo更新状态 - 关注: \(isFollowed), 点赞: \(isLiked), 收藏: \(isCollected)")
        } else {
            isFollowed = item.isFollowed
            isLiked = item.isLiked
            isCollected = item.isCollected

            print("[VideoPage] 从详情计算属性更新状态 - 关注: \(isFollowed), 点赞: \(isLiked), 收藏: \(isCollected)")
        }

        // 在预览模式下，不更新UI状态（保持预览模式的UI设置）
        if !isDraftPreviewMode {
            // 更新UI状态
            ui.updateFollowButtonState(isFollowed: isFollowed)
            ui.updateLikeButtonState(isLiked: isLiked)
            ui.updateCollectButtonState(isCollected: isCollected)

            // 隐藏关注按钮：若作品属于当前登录用户
            if item.isMyWorks == true {
                ui.followButton.isHidden = true
                print("[VideoPage] 详情确认：隐藏关注按钮 - 这是当前用户的作品")
            } else {
                ui.followButton.isHidden = false
                print("[VideoPage] 详情确认：显示关注按钮 - 这不是当前用户的作品")
            }
        } else {
            print("[VideoPage] 预览模式下跳过UI状态更新，保持预览模式设置")
            // 重新应用预览模式UI设置，确保不被其他逻辑覆盖
            ui.setPreviewModeUI(enabled: true)
        }
    }

    // 单独设置交互按钮数字的方法
    private func setupInteractionButtonCounts() {
        guard let item = videoItem else { return }

        // 点赞数
        let likeCount = item.likeNumber ?? 0
        ui.updateInteractionButtonCount(buttonIndex: 0, count: likeCount)

        // 收藏数
        let collectCount = item.collectNumber ?? 0
        ui.updateInteractionButtonCount(buttonIndex: 1, count: collectCount)

        // 分享数
        let shareCount = item.shareNumber ?? 0
        ui.updateInteractionButtonCount(buttonIndex: 2, count: shareCount)

        // 评论数不再使用item中的数据，而是通过API获取
        // 只有在没有正在进行的请求时才重置为"评论"文字
        if currentCommentRequestVideoId == nil {
            ui.updateInteractionButtonCount(buttonIndex: 3, count: 0)
            print("[VideoPage] 重置评论数显示为'评论'文字")
        } else {
            print("[VideoPage] 跳过评论数重置 - 有正在进行的请求")
        }

        print("[VideoPage] 交互按钮数字已更新 - 点赞:\(likeCount), 收藏:\(collectCount), 分享:\(shareCount), 评论:等待API获取")
    }

    // MARK: - 评论数量管理

    /// 当前评论数请求的视频ID，用于防止竞态条件
    var currentCommentRequestVideoId: Int?

    /// 重置评论数请求状态（用于页面切换时防止竞态条件）
    func resetCommentRequestState() {
        currentCommentRequestVideoId = nil
        print("[VideoPage] 重置页面 \(pageIndex) 的评论数请求状态")
    }

    /// 获取最新的评论数量
    func loadCommentCount() {
        guard let item = videoItem, let videoId = item.id else {
            print("[VideoPage] loadCommentCount 跳过 - videoItem: \(videoItem != nil), videoId: \(videoItem?.id ?? -1)")
            return
        }

        // 记录当前请求的视频ID
        currentCommentRequestVideoId = videoId

        print("[VideoPage] 开始获取评论数量 - 视频ID: \(videoId), 页面索引: \(pageIndex)")
        APIManager.shared.getCommentNumber(worksId: videoId) { [weak self] result in
            DispatchQueue.main.async {
                guard let self = self else { return }

                // 检查是否是当前视频的请求响应（防止竞态条件）
                guard self.currentCommentRequestVideoId == videoId else {
                    print("[VideoPage] 忽略过期的评论数请求响应 - 请求视频ID: \(videoId), 当前视频ID: \(self.currentCommentRequestVideoId ?? -1)")
                    return
                }

                // 验证当前videoItem是否还是同一个视频
                guard let currentItem = self.videoItem, currentItem.id == videoId else {
                    print("[VideoPage] 忽略评论数请求响应 - 视频已切换")
                    return
                }

                switch result {
                case .success(let response):
                    if response.isSuccess {
                        // 更新评论数量显示
                        self.ui.updateInteractionButtonCount(buttonIndex: 3, count: response.data)
                        print("[VideoPage] 评论数量已更新: \(response.data) (视频ID: \(videoId))")
                    } else {
                        print("[VideoPage] 获取评论数量失败：\(response.displayMessage)")
                        // 失败时显示"评论"文字
                        self.ui.updateInteractionButtonCount(buttonIndex: 3, count: 0)
                    }
                case .failure(let error):
                    print("[VideoPage] 获取评论数量网络错误：\(error.localizedDescription)")
                    // 失败时显示"评论"文字
                    self.ui.updateInteractionButtonCount(buttonIndex: 3, count: 0)
                }
            }
        }
    }

    /// 获取当前右下角评论按钮显示的数量
    func getCurrentCommentCount() -> Int {
        return ui.getCurrentCommentCount()
    }

    // 加载头像
    private func loadAvatar(from urlString: String) {
        // 处理URL，确保是完整路径
        var fullUrlString = urlString
        if !urlString.hasPrefix("http") {
            fullUrlString = "https://test-youshu.gzyoushu.com" + urlString
        }
        
        // 使用URLSession加载图片（实际项目中可能使用Kingfisher等库）
        guard let url = URL(string: fullUrlString) else { return }
        
        URLSession.shared.dataTask(with: url) { [weak self] data, response, error in
            guard let self = self,
                  let data = data,
                  let image = UIImage(data: data) else { return }
            
            DispatchQueue.main.async {
                self.ui.userAvatarImageView.image = image
            }
        }.resume()
    }
    
    private func setupPlayer() {
        // 若已初始化播放器，则直接返回，避免重复创建
        if self.player != nil {
            print("[VideoPage] 页面索引 \(pageIndex) 已存在播放器，跳过重复初始化")
            return
        }
        
        guard let item = videoItem else { return }
        
        // 检查是否有必要的播放参数
        let hasValidPlaySign = item.playSign != nil && !item.playSign!.isEmpty
        let hasValidVideoId = item.videoId != nil && !item.videoId!.isEmpty
        let hasValidURL = item.fullVideoURL != nil && !item.fullVideoURL!.isEmpty

        // 检查是否为草稿预览模式
        let isDraftPreview = isDraftPreviewMode

        // 预览模式下的验证逻辑
        if isDraftPreview {
            // 预览模式（草稿预览或内容管理预览）：只需要有videoId或者有效的URL即可
            if !hasValidVideoId && !hasValidURL {
                showVideoErrorMessage(item: item)
                return
            }
        } else {
            // 正常模式：需要有播放签名，或者同时有videoId和URL
            if !hasValidPlaySign && (!hasValidVideoId || !hasValidURL) {
                showVideoErrorMessage(item: item)
                return
            }
        }
        
        let player = TXVodPlayer()
        player.setupVideoWidget(ui.videoPlayerView, insert: 0)
        player.enableHWAcceleration = true
        player.isAutoPlay = true
        player.setRenderMode(.RENDER_MODE_FILL_EDGE)
        player.loop = true // 设置自动循环播放
        
        // 设置播放器事件回调
        player.vodDelegate = self.playerDelegate
        
        let config = TXVodPlayConfig()
        config.connectRetryCount = 3
        config.connectRetryInterval = 3
        config.timeout = 10
        
        // 使用系统 AVPlayer，避免 ThumbPlayer 依赖本地代理导致日志过多
        #if canImport(TXLiteAVSDK_UGC)
            config.playerType = TX_Enum_PlayerType.PLAYER_AVPLAYER.rawValue
        #else
            config.playerType = 0 // PLAYER_AVPLAYER
        #endif
        
        player.config = config
        
        var playStarted = false

        // 播放器启动逻辑：优先使用播放签名，然后使用URL
        if hasValidPlaySign && hasValidVideoId {
            // 使用播放签名播放（最佳方式）
            let authParams = TXPlayerAuthParams()
            authParams.appId = 1500037721
            authParams.fileId = item.videoId!
            authParams.sign = item.playSign!
            playStarted = player.startVodPlay(with: authParams) == 0

            let modeText = isDraftPreview ?
                ((parent as? VideoDisplayCenterViewController)?.videoListType == -2 ? "草稿预览" : "内容管理预览") : "正常播放"
            print("[VideoPage] \(modeText)模式：使用播放签名播放 - VideoId: \(item.videoId!)")
        } else if hasValidURL {
            // 使用URL直接播放（降级方式）
            playStarted = player.startVodPlay(item.fullVideoURL!) == 0

            let modeText = isDraftPreview ?
                ((parent as? VideoDisplayCenterViewController)?.videoListType == -2 ? "草稿预览" : "内容管理预览") : "正常播放"
            print("[VideoPage] \(modeText)模式：使用URL播放 - \(item.fullVideoURL!)")
        }
        
        if !playStarted {
            showVideoErrorMessage(item: item)
            return
        }
        
        self.player = player
        isPlaying = true

        // 确保视频类型作品显示进度条UI
        ui.showProgressBarUI()

        print("[VideoPage] 页面索引 \(pageIndex) 创建了新的播放器")
    }
    
    // 显示视频错误信息
    private func showVideoErrorMessage(item: VideoItem) {
        // 清理视频播放器视图
        ui.videoPlayerView.subviews.forEach { $0.removeFromSuperview() }
        
        // 设置黑色背景
        ui.videoPlayerView.backgroundColor = .black
        
        // 创建错误信息容器
        let errorContainer = UIView()
        errorContainer.backgroundColor = UIColor.black.withAlphaComponent(0.7)
        errorContainer.layer.cornerRadius = 10
        ui.videoPlayerView.addSubview(errorContainer)
        errorContainer.translatesAutoresizingMaskIntoConstraints = false
        
        // 创建错误图标
        let errorIcon = UIImageView()
        errorIcon.image = UIImage(systemName: "exclamationmark.triangle.fill")
        errorIcon.contentMode = .scaleAspectFit
        errorIcon.tintColor = .white
        errorContainer.addSubview(errorIcon)
        errorIcon.translatesAutoresizingMaskIntoConstraints = false
        
        // 创建错误标题
        let errorTitle = UILabel()
        errorTitle.text = "视频无法播放"
        errorTitle.textColor = .white
        errorTitle.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        errorTitle.textAlignment = .center
        errorContainer.addSubview(errorTitle)
        errorTitle.translatesAutoresizingMaskIntoConstraints = false
        
        // 创建错误详情
        let errorDetail = UILabel()
        errorDetail.text = getErrorDetailMessage(item: item)
        errorDetail.textColor = .lightGray
        errorDetail.font = UIFont.systemFont(ofSize: 14)
        errorDetail.textAlignment = .center
        errorDetail.numberOfLines = 0
        errorContainer.addSubview(errorDetail)
        errorDetail.translatesAutoresizingMaskIntoConstraints = false
        
        // 设置约束
        NSLayoutConstraint.activate([
            errorContainer.centerXAnchor.constraint(equalTo: ui.videoPlayerView.centerXAnchor),
            errorContainer.centerYAnchor.constraint(equalTo: ui.videoPlayerView.centerYAnchor),
            errorContainer.widthAnchor.constraint(equalTo: ui.videoPlayerView.widthAnchor, multiplier: 0.8),
            errorContainer.heightAnchor.constraint(lessThanOrEqualTo: ui.videoPlayerView.heightAnchor, multiplier: 0.4),
            
            errorIcon.topAnchor.constraint(equalTo: errorContainer.topAnchor, constant: 20),
            errorIcon.centerXAnchor.constraint(equalTo: errorContainer.centerXAnchor),
            errorIcon.widthAnchor.constraint(equalToConstant: 40),
            errorIcon.heightAnchor.constraint(equalToConstant: 40),
            
            errorTitle.topAnchor.constraint(equalTo: errorIcon.bottomAnchor, constant: 10),
            errorTitle.leadingAnchor.constraint(equalTo: errorContainer.leadingAnchor, constant: 20),
            errorTitle.trailingAnchor.constraint(equalTo: errorContainer.trailingAnchor, constant: -20),
            
            errorDetail.topAnchor.constraint(equalTo: errorTitle.bottomAnchor, constant: 10),
            errorDetail.leadingAnchor.constraint(equalTo: errorContainer.leadingAnchor, constant: 20),
            errorDetail.trailingAnchor.constraint(equalTo: errorContainer.trailingAnchor, constant: -20),
            errorDetail.bottomAnchor.constraint(equalTo: errorContainer.bottomAnchor, constant: -20)
        ])
        
        print("[VideoPage] 显示视频错误信息: \(errorDetail.text ?? "")")
    }
    
    // 获取详细错误信息
    private func getErrorDetailMessage(item: VideoItem) -> String {
        var details = ["视频信息不完整"]
        
        if item.videoId == nil || item.videoId!.isEmpty {
            details.append("缺少视频ID")
        }
        
        if item.playSign == nil || item.playSign!.isEmpty {
            details.append("缺少播放签名")
        }
        
        if item.fullVideoURL == nil || item.fullVideoURL!.isEmpty {
            details.append("缺少视频URL")
        }
        
        return details.joined(separator: "\n")
    }
    
    // 设置笔记视图（轮播图）
    private func setupNotesView() {
        // TODO: 实现笔记轮播图
        // 若已初始化轮播图，避免重复
        guard noteCarouselView == nil else { return }

        print("[VideoPage] Setting up NoteCarouselView for page \(pageIndex)")

        // 创建轮播组件
        let carousel = NoteCarouselView()
        noteCarouselView = carousel
        ui.videoPlayerView.addSubview(carousel)
        carousel.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 笔记类型不需要视频进度条，隐藏所有进度条相关UI
        ui.hideProgressBarUI()

        // 从数据模型提取图片 URL 数组，字段名可根据实际后端调整
        if let urls = videoItem?.worksUrl, !urls.isEmpty {
            carousel.imageURLs = urls
        }
    }

    // Note Indicator 外部逻辑已移除
    
    @objc private func interactionButtonTapped(_ sender: UIButton) {
        // 检查是否为预览模式，如果是则禁用所有交互
        if isDraftPreviewMode {
            print("[VideoPage] 预览模式下禁用交互按钮")
            return
        }

        switch sender.tag {
        case 0: // 点赞
            handleLikeAction()

        case 1: // 收藏
            handleCollectAction()

        case 2: // 分享
            // 调用代理方法处理分享
            delegate?.videoPageDidTapShare(self)

        case 3: // 评论数
            showComments()

        default:
            break
        }
    }

    // 处理点赞操作
    private func handleLikeAction() {
        guard let videoId = videoItem?.id else {
            showToast("视频信息错误")
            return
        }

        let newLikeState = !isLiked
        let operateType = 1 // 点赞
        let operateValue = newLikeState ? 1 : 2 // 1-执行 2-取消
        let worksIds = [String(videoId)]

        // 乐观UI更新
        isLiked = newLikeState
        ui.updateLikeButtonState(isLiked: isLiked)

        // 更新点赞数
        let currentCount = videoItem?.likeNumber ?? 0
        let newCount = newLikeState ? currentCount + 1 : max(0, currentCount - 1)
        ui.updateInteractionButtonCount(buttonIndex: 0, count: newCount)

        print("点赞操作: \(newLikeState ? "点赞" : "取消点赞")")

        APIManager.shared.doWorksLikeAndCollect(
            operateValue: operateValue,
            operateType: operateType,
            worksIds: worksIds
        ) { [weak self] result in
            guard let self = self else { return }

            DispatchQueue.main.async {
                switch result {
                case .success:
                    // API成功，更新数据模型
                    self.videoItem?.likeNumber = newCount
                    self.showToast(newLikeState ? "点赞成功" : "取消点赞成功")
                    print("点赞API调用成功")

                    // 通知个人主页刷新数据以同步统计数量
                    NotificationCenter.default.post(
                        name: NSNotification.Name("UserInfoUpdatedNotification"),
                        object: nil
                    )

                case .failure(let error):
                    // API失败，回滚UI状态
                    self.isLiked = !newLikeState
                    self.ui.updateLikeButtonState(isLiked: self.isLiked)
                    self.ui.updateInteractionButtonCount(buttonIndex: 0, count: currentCount)
                    self.showToast("操作失败，请稍后重试")
                    print("点赞API调用失败: \(error.localizedDescription)")
                }
            }
        }
    }

    // 处理收藏操作
    private func handleCollectAction() {
        guard let videoId = videoItem?.id else {
            showToast("视频信息错误")
            return
        }

        let newCollectState = !isCollected
        let operateType = 2 // 收藏
        let operateValue = newCollectState ? 1 : 2 // 1-执行 2-取消
        let worksIds = [String(videoId)]

        // 乐观UI更新
        isCollected = newCollectState
        ui.updateCollectButtonState(isCollected: isCollected)

        // 更新收藏数
        let currentCount = videoItem?.collectNumber ?? 0
        let newCount = newCollectState ? currentCount + 1 : max(0, currentCount - 1)
        ui.updateInteractionButtonCount(buttonIndex: 1, count: newCount)

        print("收藏操作: \(newCollectState ? "收藏" : "取消收藏")")

        APIManager.shared.doWorksLikeAndCollect(
            operateValue: operateValue,
            operateType: operateType,
            worksIds: worksIds
        ) { [weak self] result in
            guard let self = self else { return }

            DispatchQueue.main.async {
                switch result {
                case .success:
                    // API成功，更新数据模型
                    self.videoItem?.collectNumber = newCount
                    self.showToast(newCollectState ? "收藏成功" : "取消收藏成功")
                    print("收藏API调用成功")

                    // 通知个人主页刷新数据以同步统计数量
                    NotificationCenter.default.post(
                        name: NSNotification.Name("UserInfoUpdatedNotification"),
                        object: nil
                    )
                    
                case .failure(let error):
                    // API失败，回滚UI状态
                    self.isCollected = !newCollectState
                    self.ui.updateCollectButtonState(isCollected: self.isCollected)
                    self.ui.updateInteractionButtonCount(buttonIndex: 1, count: currentCount)
                    self.showToast("操作失败，请稍后重试")
                    print("收藏API调用失败: \(error.localizedDescription)")
                }
            }
        }
    }
    
    // 添加评论视图点击事件处理方法
    @objc private func commentViewTapped() {
        // 检查是否为预览模式，如果是则禁用评论功能
        if isDraftPreviewMode {
            print("[VideoPage] 预览模式下禁用评论功能")
            return
        }
        showComments()
    }

    // 显示评论的方法
    private func showComments() {
        // 确保当前有视频数据
        guard let videoId = self.videoItem?.id else {
            print("[VideoDisplay] 无法显示评论：缺少视频ID")
            return
        }
        let likeCount = self.videoItem?.likeNumber ?? 0
        let collectCount = self.videoItem?.collectNumber ?? 0

        // 获取当前右下角按钮显示的评论数量，避免闪动
        let currentCommentCount = getCurrentCommentCount()

        let commentVC = VideoCommentViewController(
            videoId: videoId,
            initialCount: currentCommentCount, // 使用右下角按钮当前显示的数量
            likeCount: likeCount,
            collectCount: collectCount,
            isLiked: isLiked,
            isCollected: isCollected
        )
        commentVC.modalPresentationStyle = .overFullScreen

        // 设置数据同步回调
        commentVC.onVideoDataChanged = { [weak self] newLikeCount, newCollectCount, newIsLiked, newIsCollected in
            DispatchQueue.main.async {
                guard let self = self else { return }

                // 更新视频页面的数据
                self.videoItem?.likeNumber = newLikeCount
                self.videoItem?.collectNumber = newCollectCount
                self.isLiked = newIsLiked
                self.isCollected = newIsCollected

                // 更新UI
                self.ui.updateLikeButtonState(isLiked: newIsLiked)
                self.ui.updateCollectButtonState(isCollected: newIsCollected)
                self.ui.updateInteractionButtonCount(buttonIndex: 0, count: newLikeCount)
                self.ui.updateInteractionButtonCount(buttonIndex: 1, count: newCollectCount)

                print("[VideoDisplay] 从评论页面同步数据 - 点赞:\(newLikeCount)(\(newIsLiked)), 收藏:\(newCollectCount)(\(newIsCollected))")
            }
        }

        // 从当前视图控制器展示评论视图
        if let currentVC = self.parent { // VideoPage 是子视图控制器，所以使用 parent
            currentVC.present(commentVC, animated: true) {
                // 评论页面展示完成后，立即获取最新评论数据
                commentVC.showComments(videoId: videoId)
                print("[VideoPage] 评论页面已展示，开始获取最新评论数据")
            }
        }

        // 监听评论页面关闭，刷新评论数量
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("CommentViewControllerDismissed"),
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.loadCommentCount()
            print("[VideoPage] 评论页面关闭，刷新评论数量")
        }
    }
    
    // 添加视频播放器点击事件
    @objc private func videoPlayerTapped() {
        if isPlaying {
            player?.pause()
        } else {
            player?.resume()
        }
        isPlaying.toggle()
        ui.updatePauseButtonVisibility(isPlaying: isPlaying)
        print("视频播放状态: \(isPlaying ? "播放" : "暂停")")
    }
    
    // 添加关注按钮点击事件
    @objc private func followButtonTapped() {
        // 检查是否为预览模式，如果是则禁用关注功能
        if isDraftPreviewMode {
            print("[VideoPage] 预览模式下禁用关注功能")
            return
        }

        // 获取作者ID
        guard let authorId = videoItem?.svUserMainVo?.customerId ?? videoItem?.customerId else {
            self.showToast("无法获取作者信息")
            return
        }

        // 当前是否已关注
        if isFollowed {
            // ---- 取消关注：弹出确认弹窗 ----
            let alert = CommonAlertView(
                title: "不再关注作者？",
                message: "",
                leftButtonTitle: "取消",
                rightButtonTitle: "不再关注"
            )
            alert.onLeftButtonTap = {
                alert.dismiss()
            }
            alert.onRightButtonTap = { [weak self] in
                guard let self = self else { return }
                // Optimistic UI 更新
                self.isFollowed = false
                self.ui.updateFollowButtonState(isFollowed: false)

                APIManager.shared.followUser‌(customerId: authorId, type: 2, worksId: self.videoItem?.id ?? 0) { [weak self] result in
                    guard let self = self else { return }
                    DispatchQueue.main.async {
                        switch result {
                        case .success:
                            self.showToast("已取消关注")
                        case .failure(let error):
                            // 回滚 UI 状态
                            self.isFollowed = true
                            self.ui.updateFollowButtonState(isFollowed: true)
                            self.showToast("取消关注失败，请稍后重试")
                            print("取消关注失败: \(error.localizedDescription)")
                        }
                    }
                }
                alert.dismiss()
            }
            let targetView = self.view.window ?? self.parent?.view ?? self.view
            alert.show(in: targetView)
        } else {
            // ---- 关注 ----
            let newFollowState = true
            // 乐观 UI 更新
            isFollowed = newFollowState
            ui.updateFollowButtonState(isFollowed: isFollowed)
            print("关注状态更新为: 已关注，开始请求接口…")

            APIManager.shared.followUser‌(customerId: authorId, type: 1, worksId: videoItem?.id ?? 0) { [weak self] result in
                guard let self = self else { return }
                DispatchQueue.main.async {
                    switch result {
                    case .success:
                        self.showToast("关注成功")
                    case .failure(let error):
                        if error.localizedDescription.contains("已关注") || error.localizedDescription.contains("无需重新关注") {
                            self.showToast("已关注该用户")
                            self.isFollowed = true
                        } else {
                            // 回退 UI
                            self.isFollowed = false
                            self.ui.updateFollowButtonState(isFollowed: false)
                            self.showToast("关注失败，请稍后重试")
                            print("关注接口调用失败: \(error.localizedDescription)")
                        }
                    }
                }
            }
        }
    }
    
    // 新增：评论下拉按钮点击事件
    @objc private func commentDropDownTapped() {
        // 检查是否为预览模式，如果是则禁用评论功能
        if isDraftPreviewMode {
            print("[VideoPage] 预览模式下禁用评论下拉功能")
            return
        }

        // TODO: 作品描述强制置顶功能未完成，后续补充
        // 当前行为：弹出评论区，与评论按钮一致
        showComments()
    }
    
    // 停止并释放播放器资源
    func stopAndReleasePlayer() {
        // 停止定时器
        progressUpdateTimer?.invalidate()
        progressUpdateTimer = nil
        // 停止轮播计时器
        noteCarouselView?.stopAutoScroll()
        
        // 确保播放器完全停止并释放
        if let player = self.player {
            // 先暂停播放
            player.pause()
            // 停止播放（这会释放底层资源）
            player.stopPlay()
            // 移除视频控件
            player.removeVideoWidget()
            // 清空代理
            player.vodDelegate = nil
            player.delegate = nil
            player.videoProcessDelegate = nil
            // 释放配置
            player.config = nil
        }
        
        // 设置为nil以释放引用
        self.player = nil
        isPlaying = false
        
        print("[VideoPage] 页面索引 \(pageIndex) 的播放器资源已完全释放")
    }
    
    // 显示播放错误
    func showPlayError(message: String) {
        // 如果已经显示了错误信息，不再重复显示
        if ui.videoPlayerView.subviews.contains(where: { $0 is UILabel && ($0 as? UILabel)?.text?.contains("视频无法播放") == true }) {
            return
        }
        
        // 创建一个简单的错误提示
        let errorLabel = UILabel()
        errorLabel.text = message
        errorLabel.textColor = .white
        errorLabel.textAlignment = .center
        errorLabel.backgroundColor = UIColor.black.withAlphaComponent(0.7)
        errorLabel.layer.cornerRadius = 8
        errorLabel.layer.masksToBounds = true
        errorLabel.font = UIFont.systemFont(ofSize: 16)
        errorLabel.numberOfLines = 0
        
        ui.videoPlayerView.addSubview(errorLabel)
        errorLabel.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            errorLabel.centerXAnchor.constraint(equalTo: ui.videoPlayerView.centerXAnchor),
            errorLabel.centerYAnchor.constraint(equalTo: ui.videoPlayerView.centerYAnchor),
            errorLabel.widthAnchor.constraint(equalTo: ui.videoPlayerView.widthAnchor, multiplier: 0.8),
            errorLabel.heightAnchor.constraint(lessThanOrEqualTo: ui.videoPlayerView.heightAnchor, multiplier: 0.2)
        ])
        
        // 3秒后自动隐藏
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            UIView.animate(withDuration: 0.3, animations: {
                errorLabel.alpha = 0
            }) { _ in
                errorLabel.removeFromSuperview()
            }
        }
    }
    
    // 公开方法：如果需要则初始化播放器
    func initializePlayerIfNeeded() {
        // 如果已有播放器，不需要重新初始化
        if player != nil {
            return
        }
        
        // 根据worksType决定是否初始化播放器
        if let item = videoItem, item.worksType == 1 || item.worksType == nil {
            // worksType=1 或未指定，初始化视频播放器
            setupPlayer()
            
            // 启动定时器，定期更新进度条
            startProgressUpdateTimer()
            
            // 确保播放状态为播放
            isPlaying = true
            ui.updatePauseButtonVisibility(isPlaying: true)
        } else if let item = videoItem, item.worksType == 2 {
            // worksType=2，笔记类型，设置轮播图
            setupNotesView()
        }
    }
    
    // 从头开始播放视频
    func restartPlayFromBeginning() {
        guard let player = player else { return }
        
        // 跳转到视频开始位置
        player.seek(0)
        
        // 恢复播放
        player.resume()
        isPlaying = true
        ui.updatePauseButtonVisibility(isPlaying: true)
        
        print("[VideoPage] 页面 \(pageIndex) 的视频从头开始播放")
    }
    
    deinit {
        // 使用完整的释放方法
        stopAndReleasePlayer()
        print("[VideoPage] 页面索引 \(pageIndex) 已释放")
    }
    
    // MARK: - 头像点击处理
    @objc private func avatarTapped() {
        guard let userId = videoItem?.svUserMainVo?.customerId ?? videoItem?.customerId else {
            return
        }

        // 检查是否是自己的头像
        if videoItem?.isMyWorks == true {
            // 如果是自己的作品，跳转到创作中心
            navigateToCreativeCenter()
        } else {
            // 如果是其他人的作品，跳转到个人主页
            navigateToPersonalHomepage(userId: userId)
        }
    }

    private func navigateToCreativeCenter() {
        let creativeCenterVC = CreativeCenterViewController()
        if let nav = self.navigationController {
            nav.pushViewController(creativeCenterVC, animated: true)
        } else {
            let navVC = UINavigationController(rootViewController: creativeCenterVC)
            navVC.modalPresentationStyle = .fullScreen
            self.present(navVC, animated: true)
        }
    }

    private func navigateToPersonalHomepage(userId: String) {
        let personalVC = PersonalHomepageViewController()
        personalVC.userId = userId
        if let nav = self.navigationController {
            nav.pushViewController(personalVC, animated: true)
        } else {
            let navVC = UINavigationController(rootViewController: personalVC)
            navVC.modalPresentationStyle = .fullScreen
            self.present(navVC, animated: true)
        }
    }
}

// MARK: - VideoProgressDragDelegate
extension VideoPage: VideoProgressDragDelegate {
    func progressDragBegan(progress: Float) {
        // 拖动开始时暂停播放，但记住之前的播放状态
        if let player = player {
            player.pause()
            // 不修改isPlaying状态，保持拖动前的状态
        }

        // 显示预览帧（如果有预览图功能）
        updateVideoFrame(at: progress)
    }

    func progressDragChanged(progress: Float) {
        // 拖动过程中更新预览帧
        updateVideoFrame(at: progress)
    }

    func progressDragEnded(progress: Float) {
        // 拖动结束时跳转到指定位置
        if let player = player {
            let targetTime = progress * Float(player.duration())
            player.seek(targetTime)

            // 如果之前是播放状态，则恢复播放
            if isPlaying {
                player.resume()
            }
        }

        // 更新UI状态
        ui.updatePauseButtonVisibility(isPlaying: isPlaying)
    }
    
    func progressTapped(progress: Float) {
        // 点击进度条时直接跳转到指定位置
        if let player = player {
            let targetTime = progress * Float(player.duration())
            player.seek(targetTime)
            
            // 如果之前是暂停状态，保持暂停
            if !isPlaying {
                player.pause()
            }
        }
    }
    
    private func updateVideoFrame(at progress: Float) {
        // 如果有播放器，尝试更新预览帧
        if let player = player {
            let targetTime = progress * Float(player.duration())
            // 这里可以使用播放器的截图功能或预览图功能
            // 由于TXVodPlayer可能没有直接的预览API，这里只是跳转到对应时间点
            player.seek(targetTime)
        }
    }
}

// MARK: - Friend Empty Placeholder
extension VideoDisplayCenterViewController {

    /// 显示朋友页面占位视图
    private func showFriendEmptyPlaceholder() {
        // 隐藏页面控制器
        ui.pageViewController.view.isHidden = true

        // 移除旧的占位视图
        friendEmptyPlaceholderView?.removeFromSuperview()

        // 创建新的占位视图
        let placeholderView = FriendEmptyPlaceholderView()
        friendEmptyPlaceholderView = placeholderView

        // 设置回调
        placeholderView.onScanTapped = { [weak self] in
            self?.handleScanTapped()
        }

        placeholderView.onWechatTapped = { [weak self] in
            self?.handleWechatTapped()
        }

        // 添加到视图
        view.addSubview(placeholderView)
        placeholderView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }

    /// 隐藏朋友页面占位视图
    private func hideFriendEmptyPlaceholder() {
        friendEmptyPlaceholderView?.removeFromSuperview()
        friendEmptyPlaceholderView = nil
        ui.pageViewController.view.isHidden = false
    }

    /// 处理扫一扫点击
    private func handleScanTapped() {
        print("朋友页面扫一扫按钮点击")
        let scanVC = QRScanViewController()
        navigationController?.pushViewController(scanVC, animated: true)
    }

    /// 处理微信好友点击
    private func handleWechatTapped() {
        print("=== 朋友页面微信分享按钮点击 ===")

        // 检查微信是否可用
        guard WXApi.isWXAppInstalled() else {
            print("❌ 微信未安装")
            showToast("请先安装微信")
            return
        }

        guard WXApi.isWXAppSupport() else {
            print("❌ 微信版本不支持")
            showToast("微信版本过低，请升级微信")
            return
        }

        // 获取当前用户信息
        APIManager.shared.getUserInfo { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let userInfo):
                    guard let data = userInfo.data,
                          !data.customerAccount.isEmpty,
                          !data.customerId.isEmpty else {
                        print("❌ 无法获取用户信息")
                        self?.showToast("用户信息加载失败，请重试")
                        return
                    }

                    let treeNumber = data.customerAccount
                    let userId = data.customerId

                    print("用户ID: \(userId)")
                    print("树小柒号: \(treeNumber)")

                    // 构建分享口令
                    let commandString = "复制到【树小柒】APP添加\(treeNumber)"
                    print("分享口令: \(commandString)")

                    // 生成二维码并分享
                    print("开始生成二维码...")
                    self?.generateUserQRCodeWithRemoteAvatar(
                        userId: userId,
                        avatarURL: data.wxAvator
                    ) { [weak self] (qrCodeImage: UIImage?) in
                        guard let qrCodeImage = qrCodeImage else {
                            print("❌ 二维码生成失败")
                            self?.showToast("二维码生成失败，请重试")
                            return
                        }

                        print("✅ 二维码生成成功，尺寸: \(qrCodeImage.size)")

                        let payload = SharePayload(
                            title: "树小柒 · 我的好友码",
                            description: commandString,
                            link: nil,
                            thumbnail: nil,
                            image: qrCodeImage,
                            type: .invite(code: treeNumber),
                            extras: ["command": commandString]
                        )

                        print("发送分享通知...")
                        NotificationCenter.default.post(name: .shareRequested, object: payload)
                        print("分享通知已发送")
                    }

                case .failure(let error):
                    print("❌ 获取用户信息失败: \(error.localizedDescription)")
                    self?.showToast("获取用户信息失败，请重试")
                }
            }
        }
    }

    // MARK: - QR Code Generation
    /// 异步加载头像并生成二维码
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - avatarURL: 头像URL
    ///   - completion: 完成回调，返回生成的二维码图片
    private func generateUserQRCodeWithRemoteAvatar(userId: String, avatarURL: String?, completion: @escaping (UIImage?) -> Void) {
        // 先生成无头像的二维码
        let qrCodeWithoutAvatar = generateUserQRCode(userId: userId)

        // 如果没有头像URL，直接返回无头像二维码
        guard let avatarURLString = avatarURL,
              !avatarURLString.isEmpty,
              let url = URL(string: avatarURLString) else {
            DispatchQueue.main.async {
                completion(qrCodeWithoutAvatar)
            }
            return
        }

        // 异步加载头像
        DispatchQueue.global().async {
            if let data = try? Data(contentsOf: url), let avatar = UIImage(data: data) {
                // 生成带头像的二维码
                let qrCodeWithAvatar = self.generateUserQRCode(userId: userId, avatar: avatar)
                DispatchQueue.main.async {
                    completion(qrCodeWithAvatar)
                }
            } else {
                // 头像加载失败，返回无头像二维码
                DispatchQueue.main.async {
                    completion(qrCodeWithoutAvatar)
                }
            }
        }
    }

    /// 生成用户分享二维码
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - avatar: 用户头像（可选）
    /// - Returns: 生成的二维码图片
    private func generateUserQRCode(userId: String, avatar: UIImage? = nil) -> UIImage? {
        // 构建二维码数据: {"content":"<customerId>", "type":1}
        let qrDict: [String: Any] = [
            "content": userId,
            "type": 1
        ]

        guard let jsonData = try? JSONSerialization.data(withJSONObject: qrDict, options: []),
              let qrData = String(data: jsonData, encoding: .utf8) else {
            print("生成二维码 JSON 字符串失败")
            return nil
        }

        return generateQRCode(from: qrData, avatar: avatar)
    }

    /// 生成二维码图片
    /// - Parameters:
    ///   - data: 二维码数据字符串
    ///   - avatar: 中心头像（可选）
    /// - Returns: 生成的二维码图片
    private func generateQRCode(from data: String, avatar: UIImage? = nil) -> UIImage? {
        guard let qrFilter = CIFilter(name: "CIQRCodeGenerator") else {
            print("无法创建二维码生成器")
            return nil
        }

        qrFilter.setValue(data.data(using: .utf8), forKey: "inputMessage")
        qrFilter.setValue("H", forKey: "inputCorrectionLevel") // 高容错率

        guard let ciImage = qrFilter.outputImage else {
            print("无法生成二维码 CIImage")
            return nil
        }

        // 放大 CIImage
        let transform = CGAffineTransform(scaleX: 10, y: 10)
        let scaledCIImage = ciImage.transformed(by: transform)

        // 将 CIImage 转换为 CGImage
        let context = CIContext()
        guard let cgImage = context.createCGImage(scaledCIImage, from: scaledCIImage.extent) else {
            print("无法创建 CGImage")
            return nil
        }
        let qrCodeImage = UIImage(cgImage: cgImage)

        // 如果没有头像，直接返回二维码
        guard let avatar = avatar else {
            return qrCodeImage
        }

        // 生成带头像的二维码
        return addAvatarToQRCode(qrCodeImage: qrCodeImage, avatar: avatar)
    }

    /// 在二维码中心添加头像
    /// - Parameters:
    ///   - qrCodeImage: 原始二维码图片
    ///   - avatar: 头像图片
    /// - Returns: 带头像的二维码图片
    private func addAvatarToQRCode(qrCodeImage: UIImage, avatar: UIImage) -> UIImage? {
        // 开始绘制带头像的二维码
        UIGraphicsBeginImageContextWithOptions(qrCodeImage.size, false, qrCodeImage.scale)
        defer { UIGraphicsEndImageContext() }

        qrCodeImage.draw(in: CGRect(origin: .zero, size: qrCodeImage.size))

        // 计算头像绘制区域（二维码中心约1/4区域）
        let avatarSize = CGSize(width: qrCodeImage.size.width * 0.25, height: qrCodeImage.size.height * 0.25)
        let avatarX = (qrCodeImage.size.width - avatarSize.width) / 2
        let avatarY = (qrCodeImage.size.height - avatarSize.height) / 2
        let avatarRect = CGRect(x: avatarX, y: avatarY, width: avatarSize.width, height: avatarSize.height)

        // 绘制白色背景（增加对比度）
        let backgroundRect = avatarRect.insetBy(dx: -4, dy: -4) // 白色边框比头像稍大
        let backgroundPath = UIBezierPath(roundedRect: backgroundRect, cornerRadius: 8)
        UIColor.white.setFill()
        backgroundPath.fill()

        // 绘制头像
        let avatarPath = UIBezierPath(roundedRect: avatarRect, cornerRadius: 8)
        avatarPath.addClip() // 裁剪绘制区域为圆角矩形
        avatar.draw(in: avatarRect)

        // 获取最终图像
        return UIGraphicsGetImageFromCurrentImageContext()
    }

    // MARK: - 观看记录
    /// 添加观看记录
    /// - Parameter videoItem: 视频数据
    private func addWatchRecord(for videoItem: VideoItem?) {
        guard let videoItem = videoItem,
              let worksId = videoItem.id else {
            print("[VideoDisplay] 添加观看记录失败：videoItem 或 worksId 为空")
            return
        }

        print("[VideoDisplay] 开始添加观看记录：worksId=\(worksId), title=\(videoItem.worksTitle ?? "未知")")

        APIManager.shared.addWorksWatch(worksId: worksId) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(_):
                    print("[VideoDisplay] 观看记录添加成功：worksId=\(worksId)")
                case .failure(let error):
                    print("[VideoDisplay] 观看记录添加失败：worksId=\(worksId), error=\(error.localizedDescription)")
                }
            }
        }
    }

    // MARK: - 分享数更新
    /// 乐观更新分享数（立即更新UI，提升用户体验）
    /// - Parameter videoItem: 要更新分享数的视频项
    func performOptimisticShareUpdate(for videoItem: VideoItem) {
        print("[VideoDisplay] 乐观更新视频分享数，视频ID: \(videoItem.id ?? -1)")

        // 查找对应的视频页面并更新分享数
        for page in videoPages {
            if let videoPage = page as? VideoPage,
               let pageVideoItem = videoPage.videoItem,
               pageVideoItem.id == videoItem.id {

                // 更新 VideoItem 的分享数
                var updatedVideoItem = pageVideoItem
                let currentShareCount = updatedVideoItem.shareNumber ?? 0
                updatedVideoItem.shareNumber = currentShareCount + 1

                // 更新页面的 VideoItem
                videoPage.videoItem = updatedVideoItem

                // 立即更新UI显示 - 只更新分享按钮的数量
                let newShareCount = updatedVideoItem.shareNumber ?? 0
                videoPage.ui.updateInteractionButtonCount(buttonIndex: 2, count: newShareCount)

                print("[VideoDisplay] 已乐观更新视频分享数：\(currentShareCount) -> \(currentShareCount + 1)")
                break
            }
        }
    }
}
